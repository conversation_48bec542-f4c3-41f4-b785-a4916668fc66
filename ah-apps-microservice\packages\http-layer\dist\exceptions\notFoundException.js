"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotFoundException = void 0;
const http_status_codes_1 = require("http-status-codes");
const applicationException_1 = require("./applicationException");
class NotFoundException extends applicationException_1.ApplicationException {
    constructor(message = 'Recurso no existe', details) {
        super(message, details, http_status_codes_1.StatusCodes.NOT_FOUND, 'NotFoundException');
    }
}
exports.NotFoundException = NotFoundException;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibm90Rm91bmRFeGNlcHRpb24uanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvZXhjZXB0aW9ucy9ub3RGb3VuZEV4Y2VwdGlvbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSx5REFBK0M7QUFDL0MsaUVBQTZEO0FBRTdELE1BQWEsaUJBQWtCLFNBQVEsMkNBQW9CO0lBQ3pELFlBQ0UsVUFBa0IsbUJBQW1CLEVBQ3JDLE9BQW1EO1FBRW5ELEtBQUssQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLCtCQUFXLENBQUMsU0FBUyxFQUFFLG1CQUFtQixDQUFDLENBQUE7SUFDckUsQ0FBQztDQUNGO0FBUEQsOENBT0MiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdGF0dXNDb2RlcyB9IGZyb20gJ2h0dHAtc3RhdHVzLWNvZGVzJ1xyXG5pbXBvcnQgeyBBcHBsaWNhdGlvbkV4Y2VwdGlvbiB9IGZyb20gJy4vYXBwbGljYXRpb25FeGNlcHRpb24nXHJcblxyXG5leHBvcnQgY2xhc3MgTm90Rm91bmRFeGNlcHRpb24gZXh0ZW5kcyBBcHBsaWNhdGlvbkV4Y2VwdGlvbiB7XHJcbiAgY29uc3RydWN0b3IoXHJcbiAgICBtZXNzYWdlOiBzdHJpbmcgPSAnUmVjdXJzbyBubyBleGlzdGUnLFxyXG4gICAgZGV0YWlscz86IEFycmF5PHsgZmllbGQ6IHN0cmluZywgbWVzc2FnZTogc3RyaW5nIH0+XHJcbiAgKSB7XHJcbiAgICBzdXBlcihtZXNzYWdlLCBkZXRhaWxzLCBTdGF0dXNDb2Rlcy5OT1RfRk9VTkQsICdOb3RGb3VuZEV4Y2VwdGlvbicpXHJcbiAgfVxyXG59XHJcbiJdfQ==