import { APIGatewayProxyResultV2, APIGatewayProxyStructuredResultV2, APIGatewayProxyResult } from 'aws-lambda';
import { Item } from '../models';
interface EnhancedAPIGatewayProxyResultV2<T> extends APIGatewayProxyStructuredResultV2 {
    body?: string | T | Record<any, string> | any;
}
interface EnhancedAPIGatewayProxyResult<T> extends APIGatewayProxyResult {
    body: string | T | Record<any, string> | any;
}
export declare const response: <T extends Item>({ statusCode, body, headers, }: EnhancedAPIGatewayProxyResultV2<T>) => APIGatewayProxyResultV2;
export declare const responseV1: <T extends Item>({ statusCode, body, headers, }: EnhancedAPIGatewayProxyResult<T>) => APIGatewayProxyResult;
export {};
