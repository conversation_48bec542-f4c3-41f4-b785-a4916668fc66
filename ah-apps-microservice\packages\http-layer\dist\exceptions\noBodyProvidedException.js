"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoBodyProvidedException = void 0;
const http_status_codes_1 = require("http-status-codes");
const applicationException_1 = require("./applicationException");
class NoBodyProvidedException extends applicationException_1.ApplicationException {
    constructor(message = 'No se proporcionó cuerpo en la solicitud', details) {
        super(message, details, http_status_codes_1.StatusCodes.BAD_REQUEST, 'NoBodyProvidedException');
    }
}
exports.NoBodyProvidedException = NoBodyProvidedException;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibm9Cb2R5UHJvdmlkZWRFeGNlcHRpb24uanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvZXhjZXB0aW9ucy9ub0JvZHlQcm92aWRlZEV4Y2VwdGlvbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSx5REFBK0M7QUFDL0MsaUVBQTZEO0FBRTdELE1BQWEsdUJBQXdCLFNBQVEsMkNBQW9CO0lBQy9ELFlBQ0UsVUFBa0IsMENBQTBDLEVBQzVELE9BQW1EO1FBRW5ELEtBQUssQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLCtCQUFXLENBQUMsV0FBVyxFQUFFLHlCQUF5QixDQUFDLENBQUE7SUFDN0UsQ0FBQztDQUNGO0FBUEQsMERBT0MiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdGF0dXNDb2RlcyB9IGZyb20gJ2h0dHAtc3RhdHVzLWNvZGVzJ1xyXG5pbXBvcnQgeyBBcHBsaWNhdGlvbkV4Y2VwdGlvbiB9IGZyb20gJy4vYXBwbGljYXRpb25FeGNlcHRpb24nXHJcblxyXG5leHBvcnQgY2xhc3MgTm9Cb2R5UHJvdmlkZWRFeGNlcHRpb24gZXh0ZW5kcyBBcHBsaWNhdGlvbkV4Y2VwdGlvbiB7XHJcbiAgY29uc3RydWN0b3IoXHJcbiAgICBtZXNzYWdlOiBzdHJpbmcgPSAnTm8gc2UgcHJvcG9yY2lvbsOzIGN1ZXJwbyBlbiBsYSBzb2xpY2l0dWQnLFxyXG4gICAgZGV0YWlscz86IEFycmF5PHsgZmllbGQ6IHN0cmluZywgbWVzc2FnZTogc3RyaW5nIH0+XHJcbiAgKSB7XHJcbiAgICBzdXBlcihtZXNzYWdlLCBkZXRhaWxzLCBTdGF0dXNDb2Rlcy5CQURfUkVRVUVTVCwgJ05vQm9keVByb3ZpZGVkRXhjZXB0aW9uJylcclxuICB9XHJcbn1cclxuIl19