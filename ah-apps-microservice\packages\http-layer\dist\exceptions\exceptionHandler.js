"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.exceptionHandler = exports.ExceptionHandlerManager = exports.GenericExceptionHandler = exports.ApplicationExceptionHandler = void 0;
const http_status_codes_1 = require("http-status-codes");
const responseUtils_1 = require("../response/responseUtils");
class ApplicationExceptionHandler {
    handle(exception) {
        const body = {
            success: false,
            error: {
                code: exception.statusCode,
                message: exception.message,
                status: exception.status,
                details: exception.details || [],
            }
        };
        return (0, responseUtils_1.response)({
            statusCode: exception.statusCode,
            body,
        });
    }
}
exports.ApplicationExceptionHandler = ApplicationExceptionHandler;
class GenericExceptionHandler {
    handle(exception) {
        const body = {
            success: false,
            error: {
                code: http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR,
                message: exception.message || http_status_codes_1.ReasonPhrases.INTERNAL_SERVER_ERROR,
                status: 'INTERNAL_SERVER_ERROR',
                details: [],
            }
        };
        return (0, responseUtils_1.response)({
            statusCode: http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR,
            body,
        });
    }
}
exports.GenericExceptionHandler = GenericExceptionHandler;
class ExceptionHandlerManager {
    constructor() {
        this.handlers = new Map();
        this.handlers.set('ApplicationException', new ApplicationExceptionHandler());
        this.handlers.set('InvalidBodyException', new ApplicationExceptionHandler());
        this.handlers.set('BadRequestException', new ApplicationExceptionHandler());
        this.handlers.set('UnauthorizedException', new ApplicationExceptionHandler());
        this.handlers.set('NotFoundException', new ApplicationExceptionHandler());
        this.handlers.set('ConflictException', new ApplicationExceptionHandler());
        this.handlers.set('InternalServerException', new ApplicationExceptionHandler());
        this.handlers.set('ItemAlreadyExistsException', new ApplicationExceptionHandler());
        this.handlers.set('NoBodyProvidedException', new ApplicationExceptionHandler());
        this.handlers.set('default', new GenericExceptionHandler());
    }
    registerHandler(exceptionType, handler) {
        this.handlers.set(exceptionType, handler);
    }
    handle(exception) {
        const exceptionType = exception.exceptionName || exception.constructor.name;
        const handler = this.handlers.get(exceptionType) || this.handlers.get('default');
        if (!handler) {
            throw new Error(`No handler found for exception type: ${exceptionType}`);
        }
        return handler.handle(exception);
    }
}
exports.ExceptionHandlerManager = ExceptionHandlerManager;
exports.exceptionHandler = new ExceptionHandlerManager();
//# sourceMappingURL=data:application/json;base64,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