import { APIGatewayProxyEventV2, APIGatewayProxyHandlerV2, APIGatewayProxyResultV2, Context } from 'aws-lambda';
import { Item } from './models';
export declare const handleAsyncV2: (handler: (_event: APIGatewayProxyEventV2, _ctx: Context) => Promise<APIGatewayProxyResultV2>) => APIGatewayProxyHandlerV2;
export declare const initializeItem: <T extends Item>(item: T, ctx: Context) => T;
export declare const initializeItemNoContext: <T extends Item>(item: T, username: string) => T;
export declare const createItem: <T extends Item>(example: new () => T, ctx: Context, event: APIGatewayProxyEventV2) => Promise<T>;
export declare const createItemNoItem: <T>(example: (new () => T), event: APIGatewayProxyEventV2) => Promise<T>;
export declare const updateItem: <T extends Item>(example: (new () => T) & Item, ctx: Context, event: APIGatewayProxyEventV2) => Promise<T>;
export declare const restPostApiInvocation: <T extends Item, R>(modelExample: new () => T, saveFunction: (item: T) => Promise<R>, successMessage?: string) => APIGatewayProxyHandlerV2;
export declare const restPutApiInvocation: <T extends Item>(modelExample: (new () => T) & Item, updateFunction: (id: string, item: T) => Promise<T>, successMessage?: string) => APIGatewayProxyHandlerV2;
export declare const restGetListApiInvocation: <T extends Item>(listFunction: () => Promise<Array<T>>, successMessage?: string) => APIGatewayProxyHandlerV2;
export declare const restGetItemApiInvocation: <T extends Item>(getFunction: (id: string) => Promise<T>, successMessage?: string) => APIGatewayProxyHandlerV2;
export declare const restPostApiInvocationWithDifferentTypes: <TInput, TOutput>(inputModelExample: (new () => TInput), saveFunction: (item: TInput) => Promise<TOutput>, successMessage?: string) => APIGatewayProxyHandlerV2;
