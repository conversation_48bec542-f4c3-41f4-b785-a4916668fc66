"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkSumOfFiles = exports.checkSum = void 0;
const glob_1 = __importDefault(require("glob"));
const util_1 = require("util");
const crypto = __importStar(require("crypto"));
const fs = __importStar(require("fs"));
const g = (0, util_1.promisify)(glob_1.default);
const hashCache = {};
const checkSum = async (filesOrPatterns) => {
    const files = await Promise.all(filesOrPatterns.map(async (p) => g(p)));
    const hashes = {};
    await files
        .filter(list => list.length)
        .reduce(async (p, file) => p.then(async () => new Promise(resolve => {
        if (hashCache[`${file}`]) {
            resolve(hashCache[`${file}`]);
        }
        const hash = crypto.createHash('sha1');
        hash.setEncoding('hex');
        const fileStream = fs.createReadStream(`${file}`);
        fileStream.pipe(hash, { end: false });
        fileStream.on('end', () => {
            hash.end();
            const h = hash.read().toString();
            hashCache[`${file}`] = h;
            resolve(h);
        });
    }).then(fileHash => {
        hashes[`${file}`] = fileHash;
    })), Promise.resolve());
    return hashes;
};
exports.checkSum = checkSum;
const checkSumOfFiles = async (filesOrPatterns) => {
    const fileChecksums = await (0, exports.checkSum)(filesOrPatterns);
    const hash = crypto.createHash('sha1');
    hash.update([...Object.entries(fileChecksums)]
        .map(([, _hash]) => _hash)
        .reduce((allHashes, _hash) => `${allHashes}${_hash}`, ''));
    return {
        checksum: hash.digest('hex'),
        hashes: {
            ...fileChecksums,
        },
        files: Object.keys(fileChecksums),
    };
};
exports.checkSumOfFiles = checkSumOfFiles;
//# sourceMappingURL=data:application/json;base64,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