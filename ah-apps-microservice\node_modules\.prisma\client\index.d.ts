
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model Aplicativo
 * 
 */
export type Aplicativo = $Result.DefaultSelection<Prisma.$AplicativoPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Aplicativos
 * const aplicativos = await prisma.aplicativo.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Aplicativos
   * const aplicativos = await prisma.aplicativo.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.aplicativo`: Exposes CRUD operations for the **Aplicativo** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Aplicativos
    * const aplicativos = await prisma.aplicativo.findMany()
    * ```
    */
  get aplicativo(): Prisma.AplicativoDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.10.1
   * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    Aplicativo: 'Aplicativo'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "aplicativo"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      Aplicativo: {
        payload: Prisma.$AplicativoPayload<ExtArgs>
        fields: Prisma.AplicativoFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AplicativoFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AplicativoFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload>
          }
          findFirst: {
            args: Prisma.AplicativoFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AplicativoFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload>
          }
          findMany: {
            args: Prisma.AplicativoFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload>[]
          }
          create: {
            args: Prisma.AplicativoCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload>
          }
          createMany: {
            args: Prisma.AplicativoCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AplicativoCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload>[]
          }
          delete: {
            args: Prisma.AplicativoDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload>
          }
          update: {
            args: Prisma.AplicativoUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload>
          }
          deleteMany: {
            args: Prisma.AplicativoDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AplicativoUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.AplicativoUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload>[]
          }
          upsert: {
            args: Prisma.AplicativoUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AplicativoPayload>
          }
          aggregate: {
            args: Prisma.AplicativoAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAplicativo>
          }
          groupBy: {
            args: Prisma.AplicativoGroupByArgs<ExtArgs>
            result: $Utils.Optional<AplicativoGroupByOutputType>[]
          }
          count: {
            args: Prisma.AplicativoCountArgs<ExtArgs>
            result: $Utils.Optional<AplicativoCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    aplicativo?: AplicativoOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */



  /**
   * Models
   */

  /**
   * Model Aplicativo
   */

  export type AggregateAplicativo = {
    _count: AplicativoCountAggregateOutputType | null
    _avg: AplicativoAvgAggregateOutputType | null
    _sum: AplicativoSumAggregateOutputType | null
    _min: AplicativoMinAggregateOutputType | null
    _max: AplicativoMaxAggregateOutputType | null
  }

  export type AplicativoAvgAggregateOutputType = {
    id: number | null
  }

  export type AplicativoSumAggregateOutputType = {
    id: number | null
  }

  export type AplicativoMinAggregateOutputType = {
    id: number | null
    status: string | null
    name: string | null
    key: string | null
    deleted: boolean | null
    deletedBy: string | null
    dateHourCreate: Date | null
    createdBy: string | null
    dateHourUpdate: Date | null
    updatedBy: string | null
  }

  export type AplicativoMaxAggregateOutputType = {
    id: number | null
    status: string | null
    name: string | null
    key: string | null
    deleted: boolean | null
    deletedBy: string | null
    dateHourCreate: Date | null
    createdBy: string | null
    dateHourUpdate: Date | null
    updatedBy: string | null
  }

  export type AplicativoCountAggregateOutputType = {
    id: number
    status: number
    name: number
    key: number
    deleted: number
    deletedBy: number
    dateHourCreate: number
    createdBy: number
    dateHourUpdate: number
    updatedBy: number
    _all: number
  }


  export type AplicativoAvgAggregateInputType = {
    id?: true
  }

  export type AplicativoSumAggregateInputType = {
    id?: true
  }

  export type AplicativoMinAggregateInputType = {
    id?: true
    status?: true
    name?: true
    key?: true
    deleted?: true
    deletedBy?: true
    dateHourCreate?: true
    createdBy?: true
    dateHourUpdate?: true
    updatedBy?: true
  }

  export type AplicativoMaxAggregateInputType = {
    id?: true
    status?: true
    name?: true
    key?: true
    deleted?: true
    deletedBy?: true
    dateHourCreate?: true
    createdBy?: true
    dateHourUpdate?: true
    updatedBy?: true
  }

  export type AplicativoCountAggregateInputType = {
    id?: true
    status?: true
    name?: true
    key?: true
    deleted?: true
    deletedBy?: true
    dateHourCreate?: true
    createdBy?: true
    dateHourUpdate?: true
    updatedBy?: true
    _all?: true
  }

  export type AplicativoAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Aplicativo to aggregate.
     */
    where?: AplicativoWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Aplicativos to fetch.
     */
    orderBy?: AplicativoOrderByWithRelationInput | AplicativoOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AplicativoWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Aplicativos from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Aplicativos.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Aplicativos
    **/
    _count?: true | AplicativoCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: AplicativoAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: AplicativoSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AplicativoMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AplicativoMaxAggregateInputType
  }

  export type GetAplicativoAggregateType<T extends AplicativoAggregateArgs> = {
        [P in keyof T & keyof AggregateAplicativo]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAplicativo[P]>
      : GetScalarType<T[P], AggregateAplicativo[P]>
  }




  export type AplicativoGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AplicativoWhereInput
    orderBy?: AplicativoOrderByWithAggregationInput | AplicativoOrderByWithAggregationInput[]
    by: AplicativoScalarFieldEnum[] | AplicativoScalarFieldEnum
    having?: AplicativoScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AplicativoCountAggregateInputType | true
    _avg?: AplicativoAvgAggregateInputType
    _sum?: AplicativoSumAggregateInputType
    _min?: AplicativoMinAggregateInputType
    _max?: AplicativoMaxAggregateInputType
  }

  export type AplicativoGroupByOutputType = {
    id: number
    status: string
    name: string
    key: string
    deleted: boolean
    deletedBy: string | null
    dateHourCreate: Date
    createdBy: string
    dateHourUpdate: Date | null
    updatedBy: string | null
    _count: AplicativoCountAggregateOutputType | null
    _avg: AplicativoAvgAggregateOutputType | null
    _sum: AplicativoSumAggregateOutputType | null
    _min: AplicativoMinAggregateOutputType | null
    _max: AplicativoMaxAggregateOutputType | null
  }

  type GetAplicativoGroupByPayload<T extends AplicativoGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AplicativoGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AplicativoGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AplicativoGroupByOutputType[P]>
            : GetScalarType<T[P], AplicativoGroupByOutputType[P]>
        }
      >
    >


  export type AplicativoSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    status?: boolean
    name?: boolean
    key?: boolean
    deleted?: boolean
    deletedBy?: boolean
    dateHourCreate?: boolean
    createdBy?: boolean
    dateHourUpdate?: boolean
    updatedBy?: boolean
  }, ExtArgs["result"]["aplicativo"]>

  export type AplicativoSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    status?: boolean
    name?: boolean
    key?: boolean
    deleted?: boolean
    deletedBy?: boolean
    dateHourCreate?: boolean
    createdBy?: boolean
    dateHourUpdate?: boolean
    updatedBy?: boolean
  }, ExtArgs["result"]["aplicativo"]>

  export type AplicativoSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    status?: boolean
    name?: boolean
    key?: boolean
    deleted?: boolean
    deletedBy?: boolean
    dateHourCreate?: boolean
    createdBy?: boolean
    dateHourUpdate?: boolean
    updatedBy?: boolean
  }, ExtArgs["result"]["aplicativo"]>

  export type AplicativoSelectScalar = {
    id?: boolean
    status?: boolean
    name?: boolean
    key?: boolean
    deleted?: boolean
    deletedBy?: boolean
    dateHourCreate?: boolean
    createdBy?: boolean
    dateHourUpdate?: boolean
    updatedBy?: boolean
  }

  export type AplicativoOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "status" | "name" | "key" | "deleted" | "deletedBy" | "dateHourCreate" | "createdBy" | "dateHourUpdate" | "updatedBy", ExtArgs["result"]["aplicativo"]>

  export type $AplicativoPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Aplicativo"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: number
      status: string
      name: string
      key: string
      deleted: boolean
      deletedBy: string | null
      dateHourCreate: Date
      createdBy: string
      dateHourUpdate: Date | null
      updatedBy: string | null
    }, ExtArgs["result"]["aplicativo"]>
    composites: {}
  }

  type AplicativoGetPayload<S extends boolean | null | undefined | AplicativoDefaultArgs> = $Result.GetResult<Prisma.$AplicativoPayload, S>

  type AplicativoCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<AplicativoFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AplicativoCountAggregateInputType | true
    }

  export interface AplicativoDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Aplicativo'], meta: { name: 'Aplicativo' } }
    /**
     * Find zero or one Aplicativo that matches the filter.
     * @param {AplicativoFindUniqueArgs} args - Arguments to find a Aplicativo
     * @example
     * // Get one Aplicativo
     * const aplicativo = await prisma.aplicativo.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AplicativoFindUniqueArgs>(args: SelectSubset<T, AplicativoFindUniqueArgs<ExtArgs>>): Prisma__AplicativoClient<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Aplicativo that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {AplicativoFindUniqueOrThrowArgs} args - Arguments to find a Aplicativo
     * @example
     * // Get one Aplicativo
     * const aplicativo = await prisma.aplicativo.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AplicativoFindUniqueOrThrowArgs>(args: SelectSubset<T, AplicativoFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AplicativoClient<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Aplicativo that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AplicativoFindFirstArgs} args - Arguments to find a Aplicativo
     * @example
     * // Get one Aplicativo
     * const aplicativo = await prisma.aplicativo.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AplicativoFindFirstArgs>(args?: SelectSubset<T, AplicativoFindFirstArgs<ExtArgs>>): Prisma__AplicativoClient<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Aplicativo that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AplicativoFindFirstOrThrowArgs} args - Arguments to find a Aplicativo
     * @example
     * // Get one Aplicativo
     * const aplicativo = await prisma.aplicativo.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AplicativoFindFirstOrThrowArgs>(args?: SelectSubset<T, AplicativoFindFirstOrThrowArgs<ExtArgs>>): Prisma__AplicativoClient<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Aplicativos that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AplicativoFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Aplicativos
     * const aplicativos = await prisma.aplicativo.findMany()
     * 
     * // Get first 10 Aplicativos
     * const aplicativos = await prisma.aplicativo.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const aplicativoWithIdOnly = await prisma.aplicativo.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AplicativoFindManyArgs>(args?: SelectSubset<T, AplicativoFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Aplicativo.
     * @param {AplicativoCreateArgs} args - Arguments to create a Aplicativo.
     * @example
     * // Create one Aplicativo
     * const Aplicativo = await prisma.aplicativo.create({
     *   data: {
     *     // ... data to create a Aplicativo
     *   }
     * })
     * 
     */
    create<T extends AplicativoCreateArgs>(args: SelectSubset<T, AplicativoCreateArgs<ExtArgs>>): Prisma__AplicativoClient<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Aplicativos.
     * @param {AplicativoCreateManyArgs} args - Arguments to create many Aplicativos.
     * @example
     * // Create many Aplicativos
     * const aplicativo = await prisma.aplicativo.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AplicativoCreateManyArgs>(args?: SelectSubset<T, AplicativoCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Aplicativos and returns the data saved in the database.
     * @param {AplicativoCreateManyAndReturnArgs} args - Arguments to create many Aplicativos.
     * @example
     * // Create many Aplicativos
     * const aplicativo = await prisma.aplicativo.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Aplicativos and only return the `id`
     * const aplicativoWithIdOnly = await prisma.aplicativo.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AplicativoCreateManyAndReturnArgs>(args?: SelectSubset<T, AplicativoCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Aplicativo.
     * @param {AplicativoDeleteArgs} args - Arguments to delete one Aplicativo.
     * @example
     * // Delete one Aplicativo
     * const Aplicativo = await prisma.aplicativo.delete({
     *   where: {
     *     // ... filter to delete one Aplicativo
     *   }
     * })
     * 
     */
    delete<T extends AplicativoDeleteArgs>(args: SelectSubset<T, AplicativoDeleteArgs<ExtArgs>>): Prisma__AplicativoClient<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Aplicativo.
     * @param {AplicativoUpdateArgs} args - Arguments to update one Aplicativo.
     * @example
     * // Update one Aplicativo
     * const aplicativo = await prisma.aplicativo.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AplicativoUpdateArgs>(args: SelectSubset<T, AplicativoUpdateArgs<ExtArgs>>): Prisma__AplicativoClient<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Aplicativos.
     * @param {AplicativoDeleteManyArgs} args - Arguments to filter Aplicativos to delete.
     * @example
     * // Delete a few Aplicativos
     * const { count } = await prisma.aplicativo.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AplicativoDeleteManyArgs>(args?: SelectSubset<T, AplicativoDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Aplicativos.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AplicativoUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Aplicativos
     * const aplicativo = await prisma.aplicativo.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AplicativoUpdateManyArgs>(args: SelectSubset<T, AplicativoUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Aplicativos and returns the data updated in the database.
     * @param {AplicativoUpdateManyAndReturnArgs} args - Arguments to update many Aplicativos.
     * @example
     * // Update many Aplicativos
     * const aplicativo = await prisma.aplicativo.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Aplicativos and only return the `id`
     * const aplicativoWithIdOnly = await prisma.aplicativo.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends AplicativoUpdateManyAndReturnArgs>(args: SelectSubset<T, AplicativoUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Aplicativo.
     * @param {AplicativoUpsertArgs} args - Arguments to update or create a Aplicativo.
     * @example
     * // Update or create a Aplicativo
     * const aplicativo = await prisma.aplicativo.upsert({
     *   create: {
     *     // ... data to create a Aplicativo
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Aplicativo we want to update
     *   }
     * })
     */
    upsert<T extends AplicativoUpsertArgs>(args: SelectSubset<T, AplicativoUpsertArgs<ExtArgs>>): Prisma__AplicativoClient<$Result.GetResult<Prisma.$AplicativoPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Aplicativos.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AplicativoCountArgs} args - Arguments to filter Aplicativos to count.
     * @example
     * // Count the number of Aplicativos
     * const count = await prisma.aplicativo.count({
     *   where: {
     *     // ... the filter for the Aplicativos we want to count
     *   }
     * })
    **/
    count<T extends AplicativoCountArgs>(
      args?: Subset<T, AplicativoCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AplicativoCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Aplicativo.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AplicativoAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AplicativoAggregateArgs>(args: Subset<T, AplicativoAggregateArgs>): Prisma.PrismaPromise<GetAplicativoAggregateType<T>>

    /**
     * Group by Aplicativo.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AplicativoGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AplicativoGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AplicativoGroupByArgs['orderBy'] }
        : { orderBy?: AplicativoGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AplicativoGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAplicativoGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Aplicativo model
   */
  readonly fields: AplicativoFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Aplicativo.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AplicativoClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Aplicativo model
   */
  interface AplicativoFieldRefs {
    readonly id: FieldRef<"Aplicativo", 'Int'>
    readonly status: FieldRef<"Aplicativo", 'String'>
    readonly name: FieldRef<"Aplicativo", 'String'>
    readonly key: FieldRef<"Aplicativo", 'String'>
    readonly deleted: FieldRef<"Aplicativo", 'Boolean'>
    readonly deletedBy: FieldRef<"Aplicativo", 'String'>
    readonly dateHourCreate: FieldRef<"Aplicativo", 'DateTime'>
    readonly createdBy: FieldRef<"Aplicativo", 'String'>
    readonly dateHourUpdate: FieldRef<"Aplicativo", 'DateTime'>
    readonly updatedBy: FieldRef<"Aplicativo", 'String'>
  }
    

  // Custom InputTypes
  /**
   * Aplicativo findUnique
   */
  export type AplicativoFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * Filter, which Aplicativo to fetch.
     */
    where: AplicativoWhereUniqueInput
  }

  /**
   * Aplicativo findUniqueOrThrow
   */
  export type AplicativoFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * Filter, which Aplicativo to fetch.
     */
    where: AplicativoWhereUniqueInput
  }

  /**
   * Aplicativo findFirst
   */
  export type AplicativoFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * Filter, which Aplicativo to fetch.
     */
    where?: AplicativoWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Aplicativos to fetch.
     */
    orderBy?: AplicativoOrderByWithRelationInput | AplicativoOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Aplicativos.
     */
    cursor?: AplicativoWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Aplicativos from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Aplicativos.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Aplicativos.
     */
    distinct?: AplicativoScalarFieldEnum | AplicativoScalarFieldEnum[]
  }

  /**
   * Aplicativo findFirstOrThrow
   */
  export type AplicativoFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * Filter, which Aplicativo to fetch.
     */
    where?: AplicativoWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Aplicativos to fetch.
     */
    orderBy?: AplicativoOrderByWithRelationInput | AplicativoOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Aplicativos.
     */
    cursor?: AplicativoWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Aplicativos from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Aplicativos.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Aplicativos.
     */
    distinct?: AplicativoScalarFieldEnum | AplicativoScalarFieldEnum[]
  }

  /**
   * Aplicativo findMany
   */
  export type AplicativoFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * Filter, which Aplicativos to fetch.
     */
    where?: AplicativoWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Aplicativos to fetch.
     */
    orderBy?: AplicativoOrderByWithRelationInput | AplicativoOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Aplicativos.
     */
    cursor?: AplicativoWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Aplicativos from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Aplicativos.
     */
    skip?: number
    distinct?: AplicativoScalarFieldEnum | AplicativoScalarFieldEnum[]
  }

  /**
   * Aplicativo create
   */
  export type AplicativoCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * The data needed to create a Aplicativo.
     */
    data: XOR<AplicativoCreateInput, AplicativoUncheckedCreateInput>
  }

  /**
   * Aplicativo createMany
   */
  export type AplicativoCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Aplicativos.
     */
    data: AplicativoCreateManyInput | AplicativoCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Aplicativo createManyAndReturn
   */
  export type AplicativoCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * The data used to create many Aplicativos.
     */
    data: AplicativoCreateManyInput | AplicativoCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Aplicativo update
   */
  export type AplicativoUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * The data needed to update a Aplicativo.
     */
    data: XOR<AplicativoUpdateInput, AplicativoUncheckedUpdateInput>
    /**
     * Choose, which Aplicativo to update.
     */
    where: AplicativoWhereUniqueInput
  }

  /**
   * Aplicativo updateMany
   */
  export type AplicativoUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Aplicativos.
     */
    data: XOR<AplicativoUpdateManyMutationInput, AplicativoUncheckedUpdateManyInput>
    /**
     * Filter which Aplicativos to update
     */
    where?: AplicativoWhereInput
    /**
     * Limit how many Aplicativos to update.
     */
    limit?: number
  }

  /**
   * Aplicativo updateManyAndReturn
   */
  export type AplicativoUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * The data used to update Aplicativos.
     */
    data: XOR<AplicativoUpdateManyMutationInput, AplicativoUncheckedUpdateManyInput>
    /**
     * Filter which Aplicativos to update
     */
    where?: AplicativoWhereInput
    /**
     * Limit how many Aplicativos to update.
     */
    limit?: number
  }

  /**
   * Aplicativo upsert
   */
  export type AplicativoUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * The filter to search for the Aplicativo to update in case it exists.
     */
    where: AplicativoWhereUniqueInput
    /**
     * In case the Aplicativo found by the `where` argument doesn't exist, create a new Aplicativo with this data.
     */
    create: XOR<AplicativoCreateInput, AplicativoUncheckedCreateInput>
    /**
     * In case the Aplicativo was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AplicativoUpdateInput, AplicativoUncheckedUpdateInput>
  }

  /**
   * Aplicativo delete
   */
  export type AplicativoDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
    /**
     * Filter which Aplicativo to delete.
     */
    where: AplicativoWhereUniqueInput
  }

  /**
   * Aplicativo deleteMany
   */
  export type AplicativoDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Aplicativos to delete
     */
    where?: AplicativoWhereInput
    /**
     * Limit how many Aplicativos to delete.
     */
    limit?: number
  }

  /**
   * Aplicativo without action
   */
  export type AplicativoDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Aplicativo
     */
    select?: AplicativoSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Aplicativo
     */
    omit?: AplicativoOmit<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const AplicativoScalarFieldEnum: {
    id: 'id',
    status: 'status',
    name: 'name',
    key: 'key',
    deleted: 'deleted',
    deletedBy: 'deletedBy',
    dateHourCreate: 'dateHourCreate',
    createdBy: 'createdBy',
    dateHourUpdate: 'dateHourUpdate',
    updatedBy: 'updatedBy'
  };

  export type AplicativoScalarFieldEnum = (typeof AplicativoScalarFieldEnum)[keyof typeof AplicativoScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    
  /**
   * Deep Input Types
   */


  export type AplicativoWhereInput = {
    AND?: AplicativoWhereInput | AplicativoWhereInput[]
    OR?: AplicativoWhereInput[]
    NOT?: AplicativoWhereInput | AplicativoWhereInput[]
    id?: IntFilter<"Aplicativo"> | number
    status?: StringFilter<"Aplicativo"> | string
    name?: StringFilter<"Aplicativo"> | string
    key?: StringFilter<"Aplicativo"> | string
    deleted?: BoolFilter<"Aplicativo"> | boolean
    deletedBy?: StringNullableFilter<"Aplicativo"> | string | null
    dateHourCreate?: DateTimeFilter<"Aplicativo"> | Date | string
    createdBy?: StringFilter<"Aplicativo"> | string
    dateHourUpdate?: DateTimeNullableFilter<"Aplicativo"> | Date | string | null
    updatedBy?: StringNullableFilter<"Aplicativo"> | string | null
  }

  export type AplicativoOrderByWithRelationInput = {
    id?: SortOrder
    status?: SortOrder
    name?: SortOrder
    key?: SortOrder
    deleted?: SortOrder
    deletedBy?: SortOrderInput | SortOrder
    dateHourCreate?: SortOrder
    createdBy?: SortOrder
    dateHourUpdate?: SortOrderInput | SortOrder
    updatedBy?: SortOrderInput | SortOrder
  }

  export type AplicativoWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    name_deleted?: AplicativoNameDeletedCompoundUniqueInput
    key_deleted?: AplicativoKeyDeletedCompoundUniqueInput
    AND?: AplicativoWhereInput | AplicativoWhereInput[]
    OR?: AplicativoWhereInput[]
    NOT?: AplicativoWhereInput | AplicativoWhereInput[]
    status?: StringFilter<"Aplicativo"> | string
    name?: StringFilter<"Aplicativo"> | string
    key?: StringFilter<"Aplicativo"> | string
    deleted?: BoolFilter<"Aplicativo"> | boolean
    deletedBy?: StringNullableFilter<"Aplicativo"> | string | null
    dateHourCreate?: DateTimeFilter<"Aplicativo"> | Date | string
    createdBy?: StringFilter<"Aplicativo"> | string
    dateHourUpdate?: DateTimeNullableFilter<"Aplicativo"> | Date | string | null
    updatedBy?: StringNullableFilter<"Aplicativo"> | string | null
  }, "id" | "name_deleted" | "key_deleted">

  export type AplicativoOrderByWithAggregationInput = {
    id?: SortOrder
    status?: SortOrder
    name?: SortOrder
    key?: SortOrder
    deleted?: SortOrder
    deletedBy?: SortOrderInput | SortOrder
    dateHourCreate?: SortOrder
    createdBy?: SortOrder
    dateHourUpdate?: SortOrderInput | SortOrder
    updatedBy?: SortOrderInput | SortOrder
    _count?: AplicativoCountOrderByAggregateInput
    _avg?: AplicativoAvgOrderByAggregateInput
    _max?: AplicativoMaxOrderByAggregateInput
    _min?: AplicativoMinOrderByAggregateInput
    _sum?: AplicativoSumOrderByAggregateInput
  }

  export type AplicativoScalarWhereWithAggregatesInput = {
    AND?: AplicativoScalarWhereWithAggregatesInput | AplicativoScalarWhereWithAggregatesInput[]
    OR?: AplicativoScalarWhereWithAggregatesInput[]
    NOT?: AplicativoScalarWhereWithAggregatesInput | AplicativoScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Aplicativo"> | number
    status?: StringWithAggregatesFilter<"Aplicativo"> | string
    name?: StringWithAggregatesFilter<"Aplicativo"> | string
    key?: StringWithAggregatesFilter<"Aplicativo"> | string
    deleted?: BoolWithAggregatesFilter<"Aplicativo"> | boolean
    deletedBy?: StringNullableWithAggregatesFilter<"Aplicativo"> | string | null
    dateHourCreate?: DateTimeWithAggregatesFilter<"Aplicativo"> | Date | string
    createdBy?: StringWithAggregatesFilter<"Aplicativo"> | string
    dateHourUpdate?: DateTimeNullableWithAggregatesFilter<"Aplicativo"> | Date | string | null
    updatedBy?: StringNullableWithAggregatesFilter<"Aplicativo"> | string | null
  }

  export type AplicativoCreateInput = {
    status?: string
    name: string
    key: string
    deleted?: boolean
    deletedBy?: string | null
    dateHourCreate?: Date | string
    createdBy: string
    dateHourUpdate?: Date | string | null
    updatedBy?: string | null
  }

  export type AplicativoUncheckedCreateInput = {
    id?: number
    status?: string
    name: string
    key: string
    deleted?: boolean
    deletedBy?: string | null
    dateHourCreate?: Date | string
    createdBy: string
    dateHourUpdate?: Date | string | null
    updatedBy?: string | null
  }

  export type AplicativoUpdateInput = {
    status?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    key?: StringFieldUpdateOperationsInput | string
    deleted?: BoolFieldUpdateOperationsInput | boolean
    deletedBy?: NullableStringFieldUpdateOperationsInput | string | null
    dateHourCreate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdBy?: StringFieldUpdateOperationsInput | string
    dateHourUpdate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updatedBy?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type AplicativoUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    status?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    key?: StringFieldUpdateOperationsInput | string
    deleted?: BoolFieldUpdateOperationsInput | boolean
    deletedBy?: NullableStringFieldUpdateOperationsInput | string | null
    dateHourCreate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdBy?: StringFieldUpdateOperationsInput | string
    dateHourUpdate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updatedBy?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type AplicativoCreateManyInput = {
    id?: number
    status?: string
    name: string
    key: string
    deleted?: boolean
    deletedBy?: string | null
    dateHourCreate?: Date | string
    createdBy: string
    dateHourUpdate?: Date | string | null
    updatedBy?: string | null
  }

  export type AplicativoUpdateManyMutationInput = {
    status?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    key?: StringFieldUpdateOperationsInput | string
    deleted?: BoolFieldUpdateOperationsInput | boolean
    deletedBy?: NullableStringFieldUpdateOperationsInput | string | null
    dateHourCreate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdBy?: StringFieldUpdateOperationsInput | string
    dateHourUpdate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updatedBy?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type AplicativoUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    status?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    key?: StringFieldUpdateOperationsInput | string
    deleted?: BoolFieldUpdateOperationsInput | boolean
    deletedBy?: NullableStringFieldUpdateOperationsInput | string | null
    dateHourCreate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdBy?: StringFieldUpdateOperationsInput | string
    dateHourUpdate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updatedBy?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type AplicativoNameDeletedCompoundUniqueInput = {
    name: string
    deleted: boolean
  }

  export type AplicativoKeyDeletedCompoundUniqueInput = {
    key: string
    deleted: boolean
  }

  export type AplicativoCountOrderByAggregateInput = {
    id?: SortOrder
    status?: SortOrder
    name?: SortOrder
    key?: SortOrder
    deleted?: SortOrder
    deletedBy?: SortOrder
    dateHourCreate?: SortOrder
    createdBy?: SortOrder
    dateHourUpdate?: SortOrder
    updatedBy?: SortOrder
  }

  export type AplicativoAvgOrderByAggregateInput = {
    id?: SortOrder
  }

  export type AplicativoMaxOrderByAggregateInput = {
    id?: SortOrder
    status?: SortOrder
    name?: SortOrder
    key?: SortOrder
    deleted?: SortOrder
    deletedBy?: SortOrder
    dateHourCreate?: SortOrder
    createdBy?: SortOrder
    dateHourUpdate?: SortOrder
    updatedBy?: SortOrder
  }

  export type AplicativoMinOrderByAggregateInput = {
    id?: SortOrder
    status?: SortOrder
    name?: SortOrder
    key?: SortOrder
    deleted?: SortOrder
    deletedBy?: SortOrder
    dateHourCreate?: SortOrder
    createdBy?: SortOrder
    dateHourUpdate?: SortOrder
    updatedBy?: SortOrder
  }

  export type AplicativoSumOrderByAggregateInput = {
    id?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}