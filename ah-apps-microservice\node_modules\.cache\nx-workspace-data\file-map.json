{"version": "6.0", "nxVersion": "20.8.2", "pathMappings": {"@microsip/util": ["packages/util/src"], "@microsip/constructs": ["packages/constructs/src"], "@microsip/models": ["packages/models/src"], "@microsip/domain": ["packages/domain/src"], "@microsip/lambda-package-utils": ["packages/lambda-package-utils/src"], "@microsip/common-layer": ["packages/common-layer/src"], "@microsip/http-layer": ["packages/http-layer/src"], "@microsip/ah-create-apps-lambda": ["packages/ah-create-apps-lambda/src"], "@microsip/ah-update-apps-lambda": ["packages/ah-update-apps-lambda/src"], "@microsip/ah-get-app-by-id-lambda": ["packages/ah-get-app-by-id-lambda/src"], "@microsip/ah-soft-delete-app-lambda": ["packages/ah-soft-delete-app-lambda/src"], "@microsip/ah-get-app-by-key-lambda": ["packages/ah-get-app-by-key-lambda/src"], "@microsip/ah-get-apps-lambda": ["packages/ah-get-apps-lambda/src"]}, "nxJsonPlugins": [], "fileMap": {"projectFileMap": {"@microsip/common-layer": [{"file": "packages/common-layer/.gitignore", "hash": "16271843823311719970"}, {"file": "packages/common-layer/README.md", "hash": "3651964345206370916"}, {"file": "packages/common-layer/client.ts", "hash": "9062730925811772934"}, {"file": "packages/common-layer/nodejs/.gitkeep", "hash": "3244421341483603138"}, {"file": "packages/common-layer/package.json", "hash": "15623768488597451448"}, {"file": "packages/common-layer/prisma/migrations/20250605182734_init/migration.sql", "hash": "879892932199337980"}, {"file": "packages/common-layer/prisma/migrations/20250620205100_english/migration.sql", "hash": "16868743270974199279"}, {"file": "packages/common-layer/prisma/migrations/migration_lock.toml", "hash": "872437952673290735"}, {"file": "packages/common-layer/prisma/schema.prisma", "hash": "1190698924034663397"}, {"file": "packages/common-layer/scripts/build.sh", "hash": "2473523250970813029"}, {"file": "packages/common-layer/scripts/post-build.sh", "hash": "8481401798554435234"}], "@microsip/ah-create-apps-lambda": [{"file": "packages/ah-create-apps-lambda/README.md", "hash": "7766320742307152983"}, {"file": "packages/ah-create-apps-lambda/package.json", "hash": "4495012230972349269", "deps": ["@microsip/domain", "@microsip/models", "@microsip/http-layer"]}, {"file": "packages/ah-create-apps-lambda/src/api.ts", "hash": "14512558822275146436"}, {"file": "packages/ah-create-apps-lambda/src/index.ts", "hash": "6852978823061117751"}, {"file": "packages/ah-create-apps-lambda/src/service.ts", "hash": "2635648007044001392"}, {"file": "packages/ah-create-apps-lambda/tsconfig.json", "hash": "10472735780535810928"}], "@microsip/ah-get-apps-lambda": [{"file": "packages/ah-get-apps-lambda/README.md", "hash": "1205381490881653247"}, {"file": "packages/ah-get-apps-lambda/package.json", "hash": "15600129090419735032", "deps": ["@microsip/domain", "@microsip/http-layer", "@microsip/models"]}, {"file": "packages/ah-get-apps-lambda/src/api.ts", "hash": "7092088802818501289"}, {"file": "packages/ah-get-apps-lambda/src/index.ts", "hash": "6892716537430408989"}, {"file": "packages/ah-get-apps-lambda/src/service.ts", "hash": "1846635878113735492"}, {"file": "packages/ah-get-apps-lambda/tsconfig.json", "hash": "10472735780535810928"}], "@microsip/ah-soft-delete-app-lambda": [{"file": "packages/ah-soft-delete-app-lambda/README.md", "hash": "3244421341483603138"}, {"file": "packages/ah-soft-delete-app-lambda/package.json", "hash": "11864875405966061269", "deps": ["@microsip/domain", "@microsip/models", "@microsip/http-layer"]}, {"file": "packages/ah-soft-delete-app-lambda/src/api.ts", "hash": "11133650557687459420"}, {"file": "packages/ah-soft-delete-app-lambda/src/index.ts", "hash": "17688668641276102209"}, {"file": "packages/ah-soft-delete-app-lambda/src/service.ts", "hash": "12186760804050928321"}, {"file": "packages/ah-soft-delete-app-lambda/tsconfig.json", "hash": "10472735780535810928"}], "@microsip/http-layer": [{"file": "packages/http-layer/.gitignore", "hash": "17802912878610692451"}, {"file": "packages/http-layer/README.md", "hash": "12706100204516885718"}, {"file": "packages/http-layer/examples/index.ts", "hash": "3244421341483603138"}, {"file": "packages/http-layer/examples/middlewareExample.ts", "hash": "12940527200028037766"}, {"file": "packages/http-layer/jest.config.ts", "hash": "1532411649933189795"}, {"file": "packages/http-layer/package.json", "hash": "8760874899777219309"}, {"file": "packages/http-layer/src/apiGateway.ts", "hash": "1529715606536769328"}, {"file": "packages/http-layer/src/enums/corsHeaders.ts", "hash": "8944111333137720989"}, {"file": "packages/http-layer/src/enums/httpMethod.ts", "hash": "6586725916466531334"}, {"file": "packages/http-layer/src/enums/index.ts", "hash": "8313583509707775222"}, {"file": "packages/http-layer/src/exceptions/applicationException.ts", "hash": "7293892962336784461"}, {"file": "packages/http-layer/src/exceptions/badRequestException.ts", "hash": "1334518344793813811"}, {"file": "packages/http-layer/src/exceptions/conflictException.ts", "hash": "4289372976687839322"}, {"file": "packages/http-layer/src/exceptions/exceptionHandler.ts", "hash": "9645055260911360542"}, {"file": "packages/http-layer/src/exceptions/index.ts", "hash": "15243365961705958254"}, {"file": "packages/http-layer/src/exceptions/internalServerException.ts", "hash": "11450350946732081189"}, {"file": "packages/http-layer/src/exceptions/invalidBodyException.ts", "hash": "10639777787297556611"}, {"file": "packages/http-layer/src/exceptions/itemAlreadyExistsException.ts", "hash": "11206958009897764152"}, {"file": "packages/http-layer/src/exceptions/noBodyProvidedException.ts", "hash": "12963385649123358093"}, {"file": "packages/http-layer/src/exceptions/notFoundException.ts", "hash": "16142049659728313316"}, {"file": "packages/http-layer/src/exceptions/unauthorizedException.ts", "hash": "4112975956292999636"}, {"file": "packages/http-layer/src/index.ts", "hash": "13055169798659024987"}, {"file": "packages/http-layer/src/middleware/errorMiddleware.ts", "hash": "9813263533221718198"}, {"file": "packages/http-layer/src/middleware/index.ts", "hash": "1803897304657294636"}, {"file": "packages/http-layer/src/models/index.ts", "hash": "8924437768552498571"}, {"file": "packages/http-layer/src/models/item.ts", "hash": "12494683527921049184"}, {"file": "packages/http-layer/src/response/apiResponseManager.ts", "hash": "12999700107144815282"}, {"file": "packages/http-layer/src/response/baseResponse.ts", "hash": "3868773054681528669"}, {"file": "packages/http-layer/src/response/index.ts", "hash": "16382117961394591578"}, {"file": "packages/http-layer/src/response/responseUtils.ts", "hash": "8207537599473527847"}, {"file": "packages/http-layer/src/response/successResponse.ts", "hash": "5157909841794311660"}, {"file": "packages/http-layer/tsconfig.json", "hash": "8920105186858792477"}], "@microsip/constructs": [{"file": "packages/constructs/README.md", "hash": "9181823848007709190"}, {"file": "packages/constructs/jest.config.ts", "hash": "9966570873889797963"}, {"file": "packages/constructs/package.json", "hash": "8581214186796035256", "deps": ["@microsip/ah-create-apps-lambda", "@microsip/ah-get-app-by-key-lambda", "@microsip/ah-get-app-by-id-lambda", "@microsip/ah-get-apps-lambda", "@microsip/ah-soft-delete-app-lambda", "@microsip/ah-update-apps-lambda", "@microsip/util"]}, {"file": "packages/constructs/src/api/builders/buildAppsCreateApi.ts", "hash": "9174902362747705462"}, {"file": "packages/constructs/src/api/builders/buildAppsDeleteApi.ts", "hash": "1536255281352479899"}, {"file": "packages/constructs/src/api/builders/buildAppsGetApi.ts", "hash": "7412891427577951232"}, {"file": "packages/constructs/src/api/builders/buildAppsGetByIdApi.ts", "hash": "17567338784818032341"}, {"file": "packages/constructs/src/api/builders/buildAppsGetByKeyApi.ts", "hash": "3447009054462856414"}, {"file": "packages/constructs/src/api/builders/buildAppsUpdateApi.ts", "hash": "12619836362703031612"}, {"file": "packages/constructs/src/api/builders/index.ts", "hash": "3840474545092315086"}, {"file": "packages/constructs/src/api/index.ts", "hash": "7845517526439980551"}, {"file": "packages/constructs/src/index.ts", "hash": "12947800960715172264"}, {"file": "packages/constructs/tsconfig.json", "hash": "3209698815256931377"}], "@microsip/models": [{"file": "packages/models/README.md", "hash": "6932389473413735039"}, {"file": "packages/models/jest.config.ts", "hash": "14202574567316552039"}, {"file": "packages/models/package.json", "hash": "12501485437946205691", "deps": ["@microsip/http-layer"]}, {"file": "packages/models/src/enums.ts", "hash": "6082502005313076957"}, {"file": "packages/models/src/index.ts", "hash": "16158366624415517772"}, {"file": "packages/models/src/mappers/appMapper.ts", "hash": "4025776164645030256"}, {"file": "packages/models/src/mappers/index.ts", "hash": "5066053244017293632"}, {"file": "packages/models/src/requests/createAppRequest.ts", "hash": "6786494733575179079"}, {"file": "packages/models/src/requests/deleteAppRequest.ts", "hash": "4058376856049153339"}, {"file": "packages/models/src/requests/index.ts", "hash": "9663081858513209697"}, {"file": "packages/models/src/requests/updateAppRequest.ts", "hash": "15956916104259416783"}, {"file": "packages/models/src/responses/appResponse.ts", "hash": "8520861356252690896"}, {"file": "packages/models/src/responses/index.ts", "hash": "1840463692657033503"}, {"file": "packages/models/tsconfig.json", "hash": "11054881020537455841"}], "@microsip/ah-get-app-by-id-lambda": [{"file": "packages/ah-get-app-by-id-lambda/README.md", "hash": "13626618702000934869"}, {"file": "packages/ah-get-app-by-id-lambda/package.json", "hash": "4503317730203051491", "deps": ["@microsip/domain", "@microsip/models", "@microsip/http-layer"]}, {"file": "packages/ah-get-app-by-id-lambda/src/api.ts", "hash": "7374667485129065086"}, {"file": "packages/ah-get-app-by-id-lambda/src/index.ts", "hash": "15973009470843552861"}, {"file": "packages/ah-get-app-by-id-lambda/src/service.ts", "hash": "14181975295650431646"}, {"file": "packages/ah-get-app-by-id-lambda/tsconfig.json", "hash": "10472735780535810928"}], "@microsip/domain": [{"file": "packages/domain/jest.config.ts", "hash": "9966570873889797963"}, {"file": "packages/domain/package.json", "hash": "9205954468476892659", "deps": ["@microsip/models"]}, {"file": "packages/domain/src/index.ts", "hash": "15862198419378085422"}, {"file": "packages/domain/src/prismaClient.ts", "hash": "8458576262997935523"}, {"file": "packages/domain/src/repositories/appsRepository.ts", "hash": "2096032165399032324"}, {"file": "packages/domain/tsconfig.json", "hash": "17779253738973652891"}], "@microsip/ah-update-apps-lambda": [{"file": "packages/ah-update-apps-lambda/README.md", "hash": "6059109076110915028"}, {"file": "packages/ah-update-apps-lambda/package.json", "hash": "10187683995366974109", "deps": ["@microsip/domain", "@microsip/models", "@microsip/http-layer"]}, {"file": "packages/ah-update-apps-lambda/src/api.ts", "hash": "16256096767213102139"}, {"file": "packages/ah-update-apps-lambda/src/index.ts", "hash": "3837799937038187852"}, {"file": "packages/ah-update-apps-lambda/src/service.ts", "hash": "4181455485806538948"}, {"file": "packages/ah-update-apps-lambda/tsconfig.json", "hash": "10472735780535810928"}], "@microsip/lambda-package-utils": [{"file": "packages/lambda-package-utils/jest.config.ts", "hash": "9966570873889797963"}, {"file": "packages/lambda-package-utils/package.json", "hash": "5442815624924231681"}, {"file": "packages/lambda-package-utils/src/checkSum.ts", "hash": "1331522859987757980"}, {"file": "packages/lambda-package-utils/src/existsOnS3.ts", "hash": "10955470629676565291"}, {"file": "packages/lambda-package-utils/src/hashDependencies.ts", "hash": "13768642025487047594"}, {"file": "packages/lambda-package-utils/src/index.ts", "hash": "6432372496912858747"}, {"file": "packages/lambda-package-utils/src/packBaseLayer.ts", "hash": "8540638775295008628"}, {"file": "packages/lambda-package-utils/src/packLambda.ts", "hash": "15811847501985194123"}, {"file": "packages/lambda-package-utils/src/packLayeredLambdas.ts", "hash": "6572246614435167215"}, {"file": "packages/lambda-package-utils/src/publishToS3.ts", "hash": "18382367986348450530"}, {"file": "packages/lambda-package-utils/src/reporter.ts", "hash": "12303522929042686717"}, {"file": "packages/lambda-package-utils/tsconfig.json", "hash": "1830145952660437791"}], "@microsip/ah-get-app-by-key-lambda": [{"file": "packages/ah-get-app-by-key-lambda/README.md", "hash": "16689330117054166913"}, {"file": "packages/ah-get-app-by-key-lambda/package.json", "hash": "1392553975837046432", "deps": ["@microsip/domain", "@microsip/models", "@microsip/http-layer"]}, {"file": "packages/ah-get-app-by-key-lambda/src/api.ts", "hash": "15813007161152734477"}, {"file": "packages/ah-get-app-by-key-lambda/src/index.ts", "hash": "4211935158380522070"}, {"file": "packages/ah-get-app-by-key-lambda/src/service.ts", "hash": "2961647690491031047"}, {"file": "packages/ah-get-app-by-key-lambda/tsconfig.json", "hash": "15434386003437463929"}], "@microsip/util": [{"file": "packages/util/.env.dummy", "hash": "791840180708607470"}, {"file": "packages/util/README.md", "hash": "6663248388498466317"}, {"file": "packages/util/jest.config.ts", "hash": "9966570873889797963"}, {"file": "packages/util/package.json", "hash": "6927548085992589969"}, {"file": "packages/util/src/check-digit.ts", "hash": "17942336447236271843"}, {"file": "packages/util/src/environment.ts", "hash": "8444463932572848089"}, {"file": "packages/util/src/index.ts", "hash": "11164189794349196970"}, {"file": "packages/util/tsconfig.json", "hash": "16396160132747673382"}], "@microsip/stack": [{"file": "packages/stack/bin/ah-apps-microservice.ts", "hash": "18409688234794069979"}, {"file": "packages/stack/jest.config.ts", "hash": "9966570873889797963"}, {"file": "packages/stack/lib/ahAppsMicroserviceStack.ts", "hash": "9698364940426654635"}, {"file": "packages/stack/lib/api/RestApiConstruct.ts", "hash": "17124422606384518255"}, {"file": "packages/stack/lib/api/index.ts", "hash": "12306140160210160846"}, {"file": "packages/stack/lib/model/index.ts", "hash": "16374559398466549338"}, {"file": "packages/stack/lib/model/lambdasDeployment.ts", "hash": "7636781740134238180"}, {"file": "packages/stack/lib/stacks/index.ts", "hash": "10080901886917357643"}, {"file": "packages/stack/lib/stacks/restApiNestedStack.ts", "hash": "5811849096285758568"}, {"file": "packages/stack/lib/util/index.ts", "hash": "4472962010771171878"}, {"file": "packages/stack/lib/util/utils.ts", "hash": "15095380836820396317"}, {"file": "packages/stack/package.json", "hash": "15742681532515691037", "deps": ["@microsip/lambda-package-utils", "@microsip/util"]}, {"file": "packages/stack/scripts/packAndPublishLambdas.ts", "hash": "13420181337775798158"}, {"file": "packages/stack/scripts/run.ts", "hash": "11808151391415557895"}, {"file": "packages/stack/test/ah-apps-microservice.test.ts", "hash": "15087854251716859307"}, {"file": "packages/stack/tsconfig.json", "hash": "13885377193458030631"}]}, "nonProjectFiles": [{"file": ".github/workflows/workflow.yml", "hash": "5650726598098335807"}, {"file": ".giti<PERSON>re", "hash": "7615364515094495757"}, {"file": ".git<PERSON><PERSON><PERSON>", "hash": "1963554843156306664"}, {"file": ".n<PERSON><PERSON><PERSON>", "hash": "6218811670181239278"}, {"file": ".prettier<PERSON>", "hash": "15380858712152270211"}, {"file": "README.md", "hash": "8476523913931307048"}, {"file": "cdk.json", "hash": "1018455221404538830"}, {"file": "eslint.config.mjs", "hash": "3852544965489575460"}, {"file": "jest.config.ts", "hash": "173027441527299385"}, {"file": "lerna.json", "hash": "1252597941464308690"}, {"file": "package.json", "hash": "17093225809922091610"}, {"file": "tsconfig.build.json", "hash": "13600666177252911355"}, {"file": "tsconfig.eslint.json", "hash": "17123755432341252873"}, {"file": "tsconfig.json", "hash": "11179770782287526940"}, {"file": "yarn.lock", "hash": "18083221180007554162"}]}}