"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuccessResponseFactory = exports.PermanentRedirectResponse = exports.MovedPermanentlyResponse = exports.ListResponse = exports.DeletedResponse = exports.UpdatedResponse = exports.CreatedResponse = exports.OkResponse = exports.BaseSuccessResponse = void 0;
const http_status_codes_1 = require("http-status-codes");
const responseUtils_1 = require("./responseUtils");
class BaseSuccessResponse {
    constructor(statusCode, defaultMessage) {
        this._statusCode = statusCode;
        this._message = defaultMessage;
    }
    setData(data) {
        this._data = data;
        return this;
    }
    setMessage(message) {
        this._message = message;
        return this;
    }
    build() {
        const body = {
            success: true,
            message: this._message,
        };
        if (this._data !== undefined) {
            body.data = this._data;
        }
        return (0, responseUtils_1.response)({
            statusCode: this._statusCode,
            body,
        });
    }
}
exports.BaseSuccessResponse = BaseSuccessResponse;
class OkResponse extends BaseSuccessResponse {
    constructor() {
        super(http_status_codes_1.StatusCodes.OK, 'Operación realizada exitosamente');
    }
}
exports.OkResponse = OkResponse;
class CreatedResponse extends BaseSuccessResponse {
    constructor() {
        super(http_status_codes_1.StatusCodes.CREATED, 'Recurso creado exitosamente');
    }
}
exports.CreatedResponse = CreatedResponse;
class UpdatedResponse extends BaseSuccessResponse {
    constructor() {
        super(http_status_codes_1.StatusCodes.OK, 'Recurso actualizado exitosamente');
    }
}
exports.UpdatedResponse = UpdatedResponse;
class DeletedResponse extends BaseSuccessResponse {
    constructor() {
        super(http_status_codes_1.StatusCodes.NO_CONTENT, 'Recurso eliminado exitosamente');
    }
    build() {
        return (0, responseUtils_1.response)({
            statusCode: this._statusCode,
            body: {
                success: true,
                message: this._message,
            },
        });
    }
}
exports.DeletedResponse = DeletedResponse;
class ListResponse extends BaseSuccessResponse {
    constructor() {
        super(http_status_codes_1.StatusCodes.OK, 'Lista obtenida exitosamente');
    }
}
exports.ListResponse = ListResponse;
class MovedPermanentlyResponse extends BaseSuccessResponse {
    constructor() {
        super(http_status_codes_1.StatusCodes.MOVED_PERMANENTLY, 'Recurso movido permanentemente');
    }
    build() {
        return (0, responseUtils_1.response)({
            statusCode: this._statusCode,
            body: {
                success: true,
                message: this._message,
            },
        });
    }
}
exports.MovedPermanentlyResponse = MovedPermanentlyResponse;
class PermanentRedirectResponse extends BaseSuccessResponse {
    constructor() {
        super(http_status_codes_1.StatusCodes.PERMANENT_REDIRECT, 'Redirección permanente');
    }
    build() {
        return (0, responseUtils_1.response)({
            statusCode: this._statusCode,
            body: {
                success: true,
                message: this._message,
            },
        });
    }
}
exports.PermanentRedirectResponse = PermanentRedirectResponse;
class SuccessResponseFactory {
    static ok(data, message) {
        const builder = new OkResponse();
        if (data !== undefined)
            builder.setData(data);
        if (message)
            builder.setMessage(message);
        return builder.build();
    }
    static created(data, message) {
        const builder = new CreatedResponse();
        if (data !== undefined)
            builder.setData(data);
        if (message)
            builder.setMessage(message);
        return builder.build();
    }
    static updated(data, message) {
        const builder = new UpdatedResponse();
        if (data !== undefined)
            builder.setData(data);
        if (message)
            builder.setMessage(message);
        return builder.build();
    }
    static deleted(message) {
        const builder = new DeletedResponse();
        if (message)
            builder.setMessage(message);
        return builder.build();
    }
    static list(data, message) {
        const builder = new ListResponse();
        builder.setData(data);
        if (message)
            builder.setMessage(message);
        return builder.build();
    }
    static movedPermanently(location) {
        return (0, responseUtils_1.response)({
            statusCode: http_status_codes_1.StatusCodes.MOVED_PERMANENTLY,
            headers: {
                location,
            },
            body: {
                success: true,
                message: 'Recurso movido permanentemente',
            },
        });
    }
    static permanentRedirect(location) {
        return (0, responseUtils_1.response)({
            statusCode: http_status_codes_1.StatusCodes.PERMANENT_REDIRECT,
            headers: {
                location,
            },
            body: {
                success: true,
                message: 'Redirección permanente',
            },
        });
    }
    static custom(statusCode, data, message = 'Operación realizada exitosamente') {
        const body = {
            success: true,
            message,
        };
        if (data !== undefined) {
            body.data = data;
        }
        return (0, responseUtils_1.response)({
            statusCode,
            body,
        });
    }
}
exports.SuccessResponseFactory = SuccessResponseFactory;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3VjY2Vzc1Jlc3BvbnNlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc3JjL3Jlc3BvbnNlL3N1Y2Nlc3NSZXNwb25zZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFDQSx5REFBK0M7QUFDL0MsbURBQTBDO0FBZTFDLE1BQXNCLG1CQUFtQjtJQUt2QyxZQUFZLFVBQWtCLEVBQUUsY0FBc0I7UUFDcEQsSUFBSSxDQUFDLFdBQVcsR0FBRyxVQUFVLENBQUE7UUFDN0IsSUFBSSxDQUFDLFFBQVEsR0FBRyxjQUFjLENBQUE7SUFDaEMsQ0FBQztJQUVELE9BQU8sQ0FBQyxJQUFPO1FBQ2IsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUE7UUFDakIsT0FBTyxJQUFJLENBQUE7SUFDYixDQUFDO0lBRUQsVUFBVSxDQUFDLE9BQWU7UUFDeEIsSUFBSSxDQUFDLFFBQVEsR0FBRyxPQUFPLENBQUE7UUFDdkIsT0FBTyxJQUFJLENBQUE7SUFDYixDQUFDO0lBRUQsS0FBSztRQUNILE1BQU0sSUFBSSxHQUEwQjtZQUNsQyxPQUFPLEVBQUUsSUFBSTtZQUNiLE9BQU8sRUFBRSxJQUFJLENBQUMsUUFBUTtTQUN2QixDQUFBO1FBRUQsSUFBSSxJQUFJLENBQUMsS0FBSyxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQzdCLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQTtRQUN4QixDQUFDO1FBRUQsT0FBTyxJQUFBLHdCQUFRLEVBQUM7WUFDZCxVQUFVLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDNUIsSUFBSTtTQUNMLENBQUMsQ0FBQTtJQUNKLENBQUM7Q0FDRjtBQW5DRCxrREFtQ0M7QUFLRCxNQUFhLFVBQW9CLFNBQVEsbUJBQXNCO0lBQzdEO1FBQ0UsS0FBSyxDQUFDLCtCQUFXLENBQUMsRUFBRSxFQUFFLGtDQUFrQyxDQUFDLENBQUE7SUFDM0QsQ0FBQztDQUNGO0FBSkQsZ0NBSUM7QUFLRCxNQUFhLGVBQXlCLFNBQVEsbUJBQXNCO0lBQ2xFO1FBQ0UsS0FBSyxDQUFDLCtCQUFXLENBQUMsT0FBTyxFQUFFLDZCQUE2QixDQUFDLENBQUE7SUFDM0QsQ0FBQztDQUNGO0FBSkQsMENBSUM7QUFLRCxNQUFhLGVBQXlCLFNBQVEsbUJBQXNCO0lBQ2xFO1FBQ0UsS0FBSyxDQUFDLCtCQUFXLENBQUMsRUFBRSxFQUFFLGtDQUFrQyxDQUFDLENBQUE7SUFDM0QsQ0FBQztDQUNGO0FBSkQsMENBSUM7QUFLRCxNQUFhLGVBQWdCLFNBQVEsbUJBQW1CO0lBQ3REO1FBQ0UsS0FBSyxDQUFDLCtCQUFXLENBQUMsVUFBVSxFQUFFLGdDQUFnQyxDQUFDLENBQUE7SUFDakUsQ0FBQztJQUVELEtBQUs7UUFDSCxPQUFPLElBQUEsd0JBQVEsRUFBQztZQUNkLFVBQVUsRUFBRSxJQUFJLENBQUMsV0FBVztZQUM1QixJQUFJLEVBQUU7Z0JBQ0osT0FBTyxFQUFFLElBQUk7Z0JBQ2IsT0FBTyxFQUFFLElBQUksQ0FBQyxRQUFRO2FBQ3ZCO1NBQ0YsQ0FBQyxDQUFBO0lBQ0osQ0FBQztDQUNGO0FBZEQsMENBY0M7QUFLRCxNQUFhLFlBQXNCLFNBQVEsbUJBQXdCO0lBQ2pFO1FBQ0UsS0FBSyxDQUFDLCtCQUFXLENBQUMsRUFBRSxFQUFFLDZCQUE2QixDQUFDLENBQUE7SUFDdEQsQ0FBQztDQUNGO0FBSkQsb0NBSUM7QUFLRCxNQUFhLHdCQUF5QixTQUFRLG1CQUFtQjtJQUMvRDtRQUNFLEtBQUssQ0FBQywrQkFBVyxDQUFDLGlCQUFpQixFQUFFLGdDQUFnQyxDQUFDLENBQUE7SUFDeEUsQ0FBQztJQUVELEtBQUs7UUFDSCxPQUFPLElBQUEsd0JBQVEsRUFBQztZQUNkLFVBQVUsRUFBRSxJQUFJLENBQUMsV0FBVztZQUM1QixJQUFJLEVBQUU7Z0JBQ0osT0FBTyxFQUFFLElBQUk7Z0JBQ2IsT0FBTyxFQUFFLElBQUksQ0FBQyxRQUFRO2FBQ3ZCO1NBQ0YsQ0FBQyxDQUFBO0lBQ0osQ0FBQztDQUNGO0FBZEQsNERBY0M7QUFLRCxNQUFhLHlCQUEwQixTQUFRLG1CQUFtQjtJQUNoRTtRQUNFLEtBQUssQ0FBQywrQkFBVyxDQUFDLGtCQUFrQixFQUFFLHdCQUF3QixDQUFDLENBQUE7SUFDakUsQ0FBQztJQUVELEtBQUs7UUFDSCxPQUFPLElBQUEsd0JBQVEsRUFBQztZQUNkLFVBQVUsRUFBRSxJQUFJLENBQUMsV0FBVztZQUM1QixJQUFJLEVBQUU7Z0JBQ0osT0FBTyxFQUFFLElBQUk7Z0JBQ2IsT0FBTyxFQUFFLElBQUksQ0FBQyxRQUFRO2FBQ3ZCO1NBQ0YsQ0FBQyxDQUFBO0lBQ0osQ0FBQztDQUNGO0FBZEQsOERBY0M7QUFLRCxNQUFhLHNCQUFzQjtJQUlqQyxNQUFNLENBQUMsRUFBRSxDQUFJLElBQVEsRUFBRSxPQUFnQjtRQUNyQyxNQUFNLE9BQU8sR0FBRyxJQUFJLFVBQVUsRUFBSyxDQUFBO1FBQ25DLElBQUksSUFBSSxLQUFLLFNBQVM7WUFBRSxPQUFPLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQzdDLElBQUksT0FBTztZQUFFLE9BQU8sQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLENBQUE7UUFDeEMsT0FBTyxPQUFPLENBQUMsS0FBSyxFQUFFLENBQUE7SUFDeEIsQ0FBQztJQUtELE1BQU0sQ0FBQyxPQUFPLENBQUksSUFBUSxFQUFFLE9BQWdCO1FBQzFDLE1BQU0sT0FBTyxHQUFHLElBQUksZUFBZSxFQUFLLENBQUE7UUFDeEMsSUFBSSxJQUFJLEtBQUssU0FBUztZQUFFLE9BQU8sQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDN0MsSUFBSSxPQUFPO1lBQUUsT0FBTyxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsQ0FBQTtRQUN4QyxPQUFPLE9BQU8sQ0FBQyxLQUFLLEVBQUUsQ0FBQTtJQUN4QixDQUFDO0lBS0QsTUFBTSxDQUFDLE9BQU8sQ0FBSSxJQUFRLEVBQUUsT0FBZ0I7UUFDMUMsTUFBTSxPQUFPLEdBQUcsSUFBSSxlQUFlLEVBQUssQ0FBQTtRQUN4QyxJQUFJLElBQUksS0FBSyxTQUFTO1lBQUUsT0FBTyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUM3QyxJQUFJLE9BQU87WUFBRSxPQUFPLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQ3hDLE9BQU8sT0FBTyxDQUFDLEtBQUssRUFBRSxDQUFBO0lBQ3hCLENBQUM7SUFLRCxNQUFNLENBQUMsT0FBTyxDQUFDLE9BQWdCO1FBQzdCLE1BQU0sT0FBTyxHQUFHLElBQUksZUFBZSxFQUFFLENBQUE7UUFDckMsSUFBSSxPQUFPO1lBQUUsT0FBTyxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsQ0FBQTtRQUN4QyxPQUFPLE9BQU8sQ0FBQyxLQUFLLEVBQUUsQ0FBQTtJQUN4QixDQUFDO0lBS0QsTUFBTSxDQUFDLElBQUksQ0FBSSxJQUFTLEVBQUUsT0FBZ0I7UUFDeEMsTUFBTSxPQUFPLEdBQUcsSUFBSSxZQUFZLEVBQUssQ0FBQTtRQUNyQyxPQUFPLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3JCLElBQUksT0FBTztZQUFFLE9BQU8sQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLENBQUE7UUFDeEMsT0FBTyxPQUFPLENBQUMsS0FBSyxFQUFFLENBQUE7SUFDeEIsQ0FBQztJQUtELE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFnQjtRQUN0QyxPQUFPLElBQUEsd0JBQVEsRUFBQztZQUNkLFVBQVUsRUFBRSwrQkFBVyxDQUFDLGlCQUFpQjtZQUN6QyxPQUFPLEVBQUU7Z0JBQ1AsUUFBUTthQUNUO1lBQ0QsSUFBSSxFQUFFO2dCQUNKLE9BQU8sRUFBRSxJQUFJO2dCQUNiLE9BQU8sRUFBRSxnQ0FBZ0M7YUFDMUM7U0FDRixDQUFDLENBQUE7SUFDSixDQUFDO0lBS0QsTUFBTSxDQUFDLGlCQUFpQixDQUFDLFFBQWdCO1FBQ3ZDLE9BQU8sSUFBQSx3QkFBUSxFQUFDO1lBQ2QsVUFBVSxFQUFFLCtCQUFXLENBQUMsa0JBQWtCO1lBQzFDLE9BQU8sRUFBRTtnQkFDUCxRQUFRO2FBQ1Q7WUFDRCxJQUFJLEVBQUU7Z0JBQ0osT0FBTyxFQUFFLElBQUk7Z0JBQ2IsT0FBTyxFQUFFLHdCQUF3QjthQUNsQztTQUNGLENBQUMsQ0FBQTtJQUNKLENBQUM7SUFLRCxNQUFNLENBQUMsTUFBTSxDQUNYLFVBQWtCLEVBQ2xCLElBQVEsRUFDUixVQUFrQixrQ0FBa0M7UUFFcEQsTUFBTSxJQUFJLEdBQTBCO1lBQ2xDLE9BQU8sRUFBRSxJQUFJO1lBQ2IsT0FBTztTQUNSLENBQUE7UUFFRCxJQUFJLElBQUksS0FBSyxTQUFTLEVBQUUsQ0FBQztZQUN2QixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNsQixDQUFDO1FBRUQsT0FBTyxJQUFBLHdCQUFRLEVBQUM7WUFDZCxVQUFVO1lBQ1YsSUFBSTtTQUNMLENBQUMsQ0FBQTtJQUNKLENBQUM7Q0FDRjtBQXhHRCx3REF3R0MiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBUElHYXRld2F5UHJveHlSZXN1bHRWMiB9IGZyb20gJ2F3cy1sYW1iZGEnXHJcbmltcG9ydCB7IFN0YXR1c0NvZGVzIH0gZnJvbSAnaHR0cC1zdGF0dXMtY29kZXMnXHJcbmltcG9ydCB7IHJlc3BvbnNlIH0gZnJvbSAnLi9yZXNwb25zZVV0aWxzJ1xyXG5pbXBvcnQgeyBTdWNjZXNzQXBpUmVzcG9uc2UgfSBmcm9tICcuL2Jhc2VSZXNwb25zZSdcclxuXHJcbi8qKlxyXG4gKiBJbnRlcmZheiBwYXJhIGVsIGJ1aWxkZXIgZGUgcmVzcHVlc3RhcyBleGl0b3Nhc1xyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBTdWNjZXNzUmVzcG9uc2VCdWlsZGVyPFQgPSBhbnk+IHtcclxuICBzZXREYXRhKGRhdGE6IFQpOiBTdWNjZXNzUmVzcG9uc2VCdWlsZGVyPFQ+XHJcbiAgc2V0TWVzc2FnZShtZXNzYWdlOiBzdHJpbmcpOiBTdWNjZXNzUmVzcG9uc2VCdWlsZGVyPFQ+XHJcbiAgYnVpbGQoKTogQVBJR2F0ZXdheVByb3h5UmVzdWx0VjJcclxufVxyXG5cclxuLyoqXHJcbiAqIENsYXNlIGJhc2UgcGFyYSByZXNwdWVzdGFzIGV4aXRvc2FzXHJcbiAqL1xyXG5leHBvcnQgYWJzdHJhY3QgY2xhc3MgQmFzZVN1Y2Nlc3NSZXNwb25zZTxUID0gYW55PiBpbXBsZW1lbnRzIFN1Y2Nlc3NSZXNwb25zZUJ1aWxkZXI8VD4ge1xyXG4gIHByb3RlY3RlZCBfZGF0YT86IFRcclxuICBwcm90ZWN0ZWQgX21lc3NhZ2U6IHN0cmluZ1xyXG4gIHByb3RlY3RlZCBfc3RhdHVzQ29kZTogbnVtYmVyXHJcblxyXG4gIGNvbnN0cnVjdG9yKHN0YXR1c0NvZGU6IG51bWJlciwgZGVmYXVsdE1lc3NhZ2U6IHN0cmluZykge1xyXG4gICAgdGhpcy5fc3RhdHVzQ29kZSA9IHN0YXR1c0NvZGVcclxuICAgIHRoaXMuX21lc3NhZ2UgPSBkZWZhdWx0TWVzc2FnZVxyXG4gIH1cclxuXHJcbiAgc2V0RGF0YShkYXRhOiBUKTogU3VjY2Vzc1Jlc3BvbnNlQnVpbGRlcjxUPiB7XHJcbiAgICB0aGlzLl9kYXRhID0gZGF0YVxyXG4gICAgcmV0dXJuIHRoaXNcclxuICB9XHJcblxyXG4gIHNldE1lc3NhZ2UobWVzc2FnZTogc3RyaW5nKTogU3VjY2Vzc1Jlc3BvbnNlQnVpbGRlcjxUPiB7XHJcbiAgICB0aGlzLl9tZXNzYWdlID0gbWVzc2FnZVxyXG4gICAgcmV0dXJuIHRoaXNcclxuICB9XHJcblxyXG4gIGJ1aWxkKCk6IEFQSUdhdGV3YXlQcm94eVJlc3VsdFYyIHtcclxuICAgIGNvbnN0IGJvZHk6IFN1Y2Nlc3NBcGlSZXNwb25zZTxUPiA9IHtcclxuICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgbWVzc2FnZTogdGhpcy5fbWVzc2FnZSxcclxuICAgIH1cclxuXHJcbiAgICBpZiAodGhpcy5fZGF0YSAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgIGJvZHkuZGF0YSA9IHRoaXMuX2RhdGFcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gcmVzcG9uc2Uoe1xyXG4gICAgICBzdGF0dXNDb2RlOiB0aGlzLl9zdGF0dXNDb2RlLFxyXG4gICAgICBib2R5LFxyXG4gICAgfSlcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBSZXNwdWVzdGEgZXhpdG9zYSBwYXJhIG9wZXJhY2lvbmVzIEdFVCAoMjAwIE9LKVxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIE9rUmVzcG9uc2U8VCA9IGFueT4gZXh0ZW5kcyBCYXNlU3VjY2Vzc1Jlc3BvbnNlPFQ+IHtcclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHN1cGVyKFN0YXR1c0NvZGVzLk9LLCAnT3BlcmFjacOzbiByZWFsaXphZGEgZXhpdG9zYW1lbnRlJylcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBSZXNwdWVzdGEgZXhpdG9zYSBwYXJhIG9wZXJhY2lvbmVzIFBPU1QgKDIwMSBDcmVhdGVkKVxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIENyZWF0ZWRSZXNwb25zZTxUID0gYW55PiBleHRlbmRzIEJhc2VTdWNjZXNzUmVzcG9uc2U8VD4ge1xyXG4gIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgc3VwZXIoU3RhdHVzQ29kZXMuQ1JFQVRFRCwgJ1JlY3Vyc28gY3JlYWRvIGV4aXRvc2FtZW50ZScpXHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogUmVzcHVlc3RhIGV4aXRvc2EgcGFyYSBvcGVyYWNpb25lcyBQVVQvUEFUQ0ggKDIwMCBPSylcclxuICovXHJcbmV4cG9ydCBjbGFzcyBVcGRhdGVkUmVzcG9uc2U8VCA9IGFueT4gZXh0ZW5kcyBCYXNlU3VjY2Vzc1Jlc3BvbnNlPFQ+IHtcclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHN1cGVyKFN0YXR1c0NvZGVzLk9LLCAnUmVjdXJzbyBhY3R1YWxpemFkbyBleGl0b3NhbWVudGUnKVxyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIFJlc3B1ZXN0YSBleGl0b3NhIHBhcmEgb3BlcmFjaW9uZXMgREVMRVRFICgyMDQgTm8gQ29udGVudClcclxuICovXHJcbmV4cG9ydCBjbGFzcyBEZWxldGVkUmVzcG9uc2UgZXh0ZW5kcyBCYXNlU3VjY2Vzc1Jlc3BvbnNlIHtcclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHN1cGVyKFN0YXR1c0NvZGVzLk5PX0NPTlRFTlQsICdSZWN1cnNvIGVsaW1pbmFkbyBleGl0b3NhbWVudGUnKVxyXG4gIH1cclxuXHJcbiAgYnVpbGQoKTogQVBJR2F0ZXdheVByb3h5UmVzdWx0VjIge1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlKHtcclxuICAgICAgc3RhdHVzQ29kZTogdGhpcy5fc3RhdHVzQ29kZSxcclxuICAgICAgYm9keToge1xyXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgbWVzc2FnZTogdGhpcy5fbWVzc2FnZSxcclxuICAgICAgfSxcclxuICAgIH0pXHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogUmVzcHVlc3RhIGV4aXRvc2EgcGFyYSBvcGVyYWNpb25lcyBkZSBsaXN0YWRvICgyMDAgT0spXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgTGlzdFJlc3BvbnNlPFQgPSBhbnk+IGV4dGVuZHMgQmFzZVN1Y2Nlc3NSZXNwb25zZTxUW10+IHtcclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHN1cGVyKFN0YXR1c0NvZGVzLk9LLCAnTGlzdGEgb2J0ZW5pZGEgZXhpdG9zYW1lbnRlJylcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBSZXNwdWVzdGEgZXhpdG9zYSBwYXJhIHJlZGlyZWNjaW9uZXMgcGVybWFuZW50ZXMgKDMwMSBNb3ZlZCBQZXJtYW5lbnRseSlcclxuICovXHJcbmV4cG9ydCBjbGFzcyBNb3ZlZFBlcm1hbmVudGx5UmVzcG9uc2UgZXh0ZW5kcyBCYXNlU3VjY2Vzc1Jlc3BvbnNlIHtcclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHN1cGVyKFN0YXR1c0NvZGVzLk1PVkVEX1BFUk1BTkVOVExZLCAnUmVjdXJzbyBtb3ZpZG8gcGVybWFuZW50ZW1lbnRlJylcclxuICB9XHJcblxyXG4gIGJ1aWxkKCk6IEFQSUdhdGV3YXlQcm94eVJlc3VsdFYyIHtcclxuICAgIHJldHVybiByZXNwb25zZSh7XHJcbiAgICAgIHN0YXR1c0NvZGU6IHRoaXMuX3N0YXR1c0NvZGUsXHJcbiAgICAgIGJvZHk6IHtcclxuICAgICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICAgIG1lc3NhZ2U6IHRoaXMuX21lc3NhZ2UsXHJcbiAgICAgIH0sXHJcbiAgICB9KVxyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIFJlc3B1ZXN0YSBleGl0b3NhIHBhcmEgcmVkaXJlY2Npb25lcyBwZXJtYW5lbnRlcyAoMzA4IFBlcm1hbmVudCBSZWRpcmVjdClcclxuICovXHJcbmV4cG9ydCBjbGFzcyBQZXJtYW5lbnRSZWRpcmVjdFJlc3BvbnNlIGV4dGVuZHMgQmFzZVN1Y2Nlc3NSZXNwb25zZSB7XHJcbiAgY29uc3RydWN0b3IoKSB7XHJcbiAgICBzdXBlcihTdGF0dXNDb2Rlcy5QRVJNQU5FTlRfUkVESVJFQ1QsICdSZWRpcmVjY2nDs24gcGVybWFuZW50ZScpXHJcbiAgfVxyXG5cclxuICBidWlsZCgpOiBBUElHYXRld2F5UHJveHlSZXN1bHRWMiB7XHJcbiAgICByZXR1cm4gcmVzcG9uc2Uoe1xyXG4gICAgICBzdGF0dXNDb2RlOiB0aGlzLl9zdGF0dXNDb2RlLFxyXG4gICAgICBib2R5OiB7XHJcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICBtZXNzYWdlOiB0aGlzLl9tZXNzYWdlLFxyXG4gICAgICB9LFxyXG4gICAgfSlcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBGYWN0b3J5IHBhcmEgY3JlYXIgcmVzcHVlc3RhcyBleGl0b3Nhc1xyXG4gKi9cclxuZXhwb3J0IGNsYXNzIFN1Y2Nlc3NSZXNwb25zZUZhY3Rvcnkge1xyXG4gIC8qKlxyXG4gICAqIENyZWEgdW5hIHJlc3B1ZXN0YSBPSyAoMjAwKVxyXG4gICAqL1xyXG4gIHN0YXRpYyBvazxUPihkYXRhPzogVCwgbWVzc2FnZT86IHN0cmluZyk6IEFQSUdhdGV3YXlQcm94eVJlc3VsdFYyIHtcclxuICAgIGNvbnN0IGJ1aWxkZXIgPSBuZXcgT2tSZXNwb25zZTxUPigpXHJcbiAgICBpZiAoZGF0YSAhPT0gdW5kZWZpbmVkKSBidWlsZGVyLnNldERhdGEoZGF0YSlcclxuICAgIGlmIChtZXNzYWdlKSBidWlsZGVyLnNldE1lc3NhZ2UobWVzc2FnZSlcclxuICAgIHJldHVybiBidWlsZGVyLmJ1aWxkKClcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWEgdW5hIHJlc3B1ZXN0YSBDcmVhdGVkICgyMDEpXHJcbiAgICovXHJcbiAgc3RhdGljIGNyZWF0ZWQ8VD4oZGF0YT86IFQsIG1lc3NhZ2U/OiBzdHJpbmcpOiBBUElHYXRld2F5UHJveHlSZXN1bHRWMiB7XHJcbiAgICBjb25zdCBidWlsZGVyID0gbmV3IENyZWF0ZWRSZXNwb25zZTxUPigpXHJcbiAgICBpZiAoZGF0YSAhPT0gdW5kZWZpbmVkKSBidWlsZGVyLnNldERhdGEoZGF0YSlcclxuICAgIGlmIChtZXNzYWdlKSBidWlsZGVyLnNldE1lc3NhZ2UobWVzc2FnZSlcclxuICAgIHJldHVybiBidWlsZGVyLmJ1aWxkKClcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWEgdW5hIHJlc3B1ZXN0YSBVcGRhdGVkICgyMDApXHJcbiAgICovXHJcbiAgc3RhdGljIHVwZGF0ZWQ8VD4oZGF0YT86IFQsIG1lc3NhZ2U/OiBzdHJpbmcpOiBBUElHYXRld2F5UHJveHlSZXN1bHRWMiB7XHJcbiAgICBjb25zdCBidWlsZGVyID0gbmV3IFVwZGF0ZWRSZXNwb25zZTxUPigpXHJcbiAgICBpZiAoZGF0YSAhPT0gdW5kZWZpbmVkKSBidWlsZGVyLnNldERhdGEoZGF0YSlcclxuICAgIGlmIChtZXNzYWdlKSBidWlsZGVyLnNldE1lc3NhZ2UobWVzc2FnZSlcclxuICAgIHJldHVybiBidWlsZGVyLmJ1aWxkKClcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWEgdW5hIHJlc3B1ZXN0YSBEZWxldGVkICgyMDQpXHJcbiAgICovXHJcbiAgc3RhdGljIGRlbGV0ZWQobWVzc2FnZT86IHN0cmluZyk6IEFQSUdhdGV3YXlQcm94eVJlc3VsdFYyIHtcclxuICAgIGNvbnN0IGJ1aWxkZXIgPSBuZXcgRGVsZXRlZFJlc3BvbnNlKClcclxuICAgIGlmIChtZXNzYWdlKSBidWlsZGVyLnNldE1lc3NhZ2UobWVzc2FnZSlcclxuICAgIHJldHVybiBidWlsZGVyLmJ1aWxkKClcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWEgdW5hIHJlc3B1ZXN0YSBkZSBsaXN0YWRvICgyMDApXHJcbiAgICovXHJcbiAgc3RhdGljIGxpc3Q8VD4oZGF0YTogVFtdLCBtZXNzYWdlPzogc3RyaW5nKTogQVBJR2F0ZXdheVByb3h5UmVzdWx0VjIge1xyXG4gICAgY29uc3QgYnVpbGRlciA9IG5ldyBMaXN0UmVzcG9uc2U8VD4oKVxyXG4gICAgYnVpbGRlci5zZXREYXRhKGRhdGEpXHJcbiAgICBpZiAobWVzc2FnZSkgYnVpbGRlci5zZXRNZXNzYWdlKG1lc3NhZ2UpXHJcbiAgICByZXR1cm4gYnVpbGRlci5idWlsZCgpXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhIHVuYSByZXNwdWVzdGEgZGUgcmVkaXJlY2Npw7NuIHBlcm1hbmVudGUgKDMwMSlcclxuICAgKi9cclxuICBzdGF0aWMgbW92ZWRQZXJtYW5lbnRseShsb2NhdGlvbjogc3RyaW5nKTogQVBJR2F0ZXdheVByb3h5UmVzdWx0VjIge1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlKHtcclxuICAgICAgc3RhdHVzQ29kZTogU3RhdHVzQ29kZXMuTU9WRURfUEVSTUFORU5UTFksXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICBsb2NhdGlvbixcclxuICAgICAgfSxcclxuICAgICAgYm9keToge1xyXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgbWVzc2FnZTogJ1JlY3Vyc28gbW92aWRvIHBlcm1hbmVudGVtZW50ZScsXHJcbiAgICAgIH0sXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYSB1bmEgcmVzcHVlc3RhIGRlIHJlZGlyZWNjacOzbiBwZXJtYW5lbnRlICgzMDgpXHJcbiAgICovXHJcbiAgc3RhdGljIHBlcm1hbmVudFJlZGlyZWN0KGxvY2F0aW9uOiBzdHJpbmcpOiBBUElHYXRld2F5UHJveHlSZXN1bHRWMiB7XHJcbiAgICByZXR1cm4gcmVzcG9uc2Uoe1xyXG4gICAgICBzdGF0dXNDb2RlOiBTdGF0dXNDb2Rlcy5QRVJNQU5FTlRfUkVESVJFQ1QsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICBsb2NhdGlvbixcclxuICAgICAgfSxcclxuICAgICAgYm9keToge1xyXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgbWVzc2FnZTogJ1JlZGlyZWNjacOzbiBwZXJtYW5lbnRlJyxcclxuICAgICAgfSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDcmVhIHVuYSByZXNwdWVzdGEgcGVyc29uYWxpemFkYVxyXG4gICAqL1xyXG4gIHN0YXRpYyBjdXN0b208VD4oXHJcbiAgICBzdGF0dXNDb2RlOiBudW1iZXIsXHJcbiAgICBkYXRhPzogVCxcclxuICAgIG1lc3NhZ2U6IHN0cmluZyA9ICdPcGVyYWNpw7NuIHJlYWxpemFkYSBleGl0b3NhbWVudGUnXHJcbiAgKTogQVBJR2F0ZXdheVByb3h5UmVzdWx0VjIge1xyXG4gICAgY29uc3QgYm9keTogU3VjY2Vzc0FwaVJlc3BvbnNlPFQ+ID0ge1xyXG4gICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICBtZXNzYWdlLFxyXG4gICAgfVxyXG5cclxuICAgIGlmIChkYXRhICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgYm9keS5kYXRhID0gZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiByZXNwb25zZSh7XHJcbiAgICAgIHN0YXR1c0NvZGUsXHJcbiAgICAgIGJvZHksXHJcbiAgICB9KVxyXG4gIH1cclxufSAiXX0=