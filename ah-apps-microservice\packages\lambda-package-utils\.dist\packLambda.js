"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.packLambda = exports.WebpackMode = void 0;
const chalk_1 = __importDefault(require("chalk"));
const webpack_1 = __importDefault(require("webpack"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const yazl = __importStar(require("yazl"));
const glob_1 = __importDefault(require("glob"));
const util_1 = require("util");
const webpack_node_externals_1 = __importDefault(require("webpack-node-externals"));
const tsconfig_paths_webpack_plugin_1 = require("tsconfig-paths-webpack-plugin");
const existsOnS3_1 = require("./existsOnS3");
const publishToS3_1 = require("./publishToS3");
const checkSum_1 = require("./checkSum");
const reporter_1 = require("./reporter");
const g = (0, util_1.promisify)(glob_1.default);
var WebpackMode;
(function (WebpackMode) {
    WebpackMode["development"] = "development";
    WebpackMode["production"] = "production";
    WebpackMode["none"] = "none";
})(WebpackMode || (exports.WebpackMode = WebpackMode = {}));
const packLambda = async (args) => {
    var _a, _b, _c, _d;
    const { tsConfig, mode, outDir, Bucket, name, src, reporter, srcDir } = args;
    const r = reporter !== null && reporter !== void 0 ? reporter : (0, reporter_1.ConsoleProgressReporter)(name);
    const progress = (_a = r === null || r === void 0 ? void 0 : r.progress) === null || _a === void 0 ? void 0 : _a.call(r, name);
    const success = (_b = r === null || r === void 0 ? void 0 : r.success) === null || _b === void 0 ? void 0 : _b.call(r, name);
    const failure = (_c = r === null || r === void 0 ? void 0 : r.failure) === null || _c === void 0 ? void 0 : _c.call(r, name);
    const sizeInBytes = (_d = r === null || r === void 0 ? void 0 : r.sizeInBytes) === null || _d === void 0 ? void 0 : _d.call(r, name);
    try {
        fs.statSync(src);
    }
    catch (e) {
        failure === null || failure === void 0 ? void 0 : failure(`The source file ${chalk_1.default.cyan(src)} for ${chalk_1.default.green(name)} does not exist!`);
        throw e;
    }
    const filesForHash = await g(`${srcDir}/.dist/**/*`, { nodir: true });
    const deps = await (0, checkSum_1.checkSumOfFiles)(filesForHash);
    const { checksum: hash, hashes } = deps;
    const jsFilenameWithHash = `${name}-${hash}.js`;
    const zipFilenameWithHash = `${name}-${hash}-layered.zip`;
    const localPath = path.resolve(outDir, zipFilenameWithHash);
    fs.writeFileSync(path.resolve(outDir, `${name}-${hash}.hashes.json`), JSON.stringify({
        hashes,
    }, null, 2), 'utf-8');
    progress === null || progress === void 0 ? void 0 : progress('Checking if lambda exists on S3');
    let fileSize = await (0, existsOnS3_1.existsOnS3)(Bucket, zipFilenameWithHash, outDir);
    if (fileSize) {
        success === null || success === void 0 ? void 0 : success('OK');
        sizeInBytes === null || sizeInBytes === void 0 ? void 0 : sizeInBytes(fileSize);
        return {
            name,
            zipFileName: zipFilenameWithHash,
            dependencies: deps,
        };
    }
    try {
        const { size } = fs.statSync(localPath);
        success === null || success === void 0 ? void 0 : success('OK');
        sizeInBytes === null || sizeInBytes === void 0 ? void 0 : sizeInBytes(size);
        progress === null || progress === void 0 ? void 0 : progress('Publishing to S3', `-> ${Bucket}`);
        await (0, publishToS3_1.publishToS3)(Bucket, zipFilenameWithHash, localPath);
        await (0, existsOnS3_1.existsOnS3)(Bucket, zipFilenameWithHash, outDir);
        return {
            name,
            zipFileName: zipFilenameWithHash,
            dependencies: deps,
        };
    }
    catch {
    }
    progress === null || progress === void 0 ? void 0 : progress('Packing');
    await new Promise((resolve, reject) => (0, webpack_1.default)({
        entry: [src],
        mode,
        target: 'node',
        externals: [(0, webpack_node_externals_1.default)()],
        module: {
            rules: [
                {
                    test: /\.ts$/,
                    loader: 'ts-loader',
                    exclude: /node_modules/,
                    options: {
                        configFile: tsConfig,
                        transpileOnly: true,
                        experimentalWatchApi: true,
                    },
                },
            ],
        },
        optimization: {
            removeAvailableModules: false,
            removeEmptyChunks: false,
            splitChunks: false,
        },
        resolve: {
            extensions: ['.ts', '.js'],
            plugins: [
                new tsconfig_paths_webpack_plugin_1.TsconfigPathsPlugin({
                    configFile: tsConfig,
                    logLevel: 'INFO',
                    extensions: ['.ts', '.js'],
                }),
            ],
        },
        output: {
            path: outDir,
            libraryTarget: 'umd',
            filename: jsFilenameWithHash,
        },
    }, (err, stats) => {
        var _a;
        if ((err !== null && err !== undefined) || (stats === null || stats === void 0 ? void 0 : stats.hasErrors())) {
            failure === null || failure === void 0 ? void 0 : failure('webpack failed', err === null || err === void 0 ? void 0 : err.message);
            console.error(err);
            if ((_a = stats === null || stats === void 0 ? void 0 : stats.hasErrors()) !== null && _a !== void 0 ? _a : false) {
                console.info(stats === null || stats === void 0 ? void 0 : stats.toString());
                console.info(stats === null || stats === void 0 ? void 0 : stats.toJson());
            }
            reject(err);
        }
        const f = path.resolve(outDir, jsFilenameWithHash);
        progress === null || progress === void 0 ? void 0 : progress('Creating archive Paco');
        const zipfile = new yazl.ZipFile();
        zipfile.addFile(f, 'index.js');
        zipfile.addBuffer(Buffer.from(JSON.stringify(hashes, null, 2)), 'hashes.json');
        zipfile.outputStream.pipe(fs.createWriteStream(localPath)).on('close', () => {
            success === null || success === void 0 ? void 0 : success('Lambda packed', `${Math.round(fs.statSync(localPath).size / 1024)}KB`);
            resolve();
        });
        zipfile.end();
    }));
    progress === null || progress === void 0 ? void 0 : progress('Publishing to S3', `-> ${Bucket}`);
    await (0, publishToS3_1.publishToS3)(Bucket, zipFilenameWithHash, localPath);
    fileSize = await (0, existsOnS3_1.existsOnS3)(Bucket, zipFilenameWithHash, outDir);
    sizeInBytes === null || sizeInBytes === void 0 ? void 0 : sizeInBytes(fileSize);
    success === null || success === void 0 ? void 0 : success('All done');
    return {
        zipFileName: zipFilenameWithHash,
        name,
        dependencies: deps,
    };
};
exports.packLambda = packLambda;
//# sourceMappingURL=data:application/json;base64,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