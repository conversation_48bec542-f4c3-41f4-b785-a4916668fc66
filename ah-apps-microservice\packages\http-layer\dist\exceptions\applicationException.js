"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationException = void 0;
const http_status_codes_1 = require("http-status-codes");
class ApplicationException extends Error {
    constructor(message, details, statusCode = http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR, exceptionName = 'ApplicationException') {
        super(message);
        this.message = message;
        this.statusCode = statusCode;
        super.name = 'ApplicationException';
        this.exceptionName = exceptionName;
        if (details && details.length > 0) {
            this.details = details;
        }
        else {
            this.details = [];
        }
    }
    get status() {
        return this.getStatusFromCode(this.statusCode);
    }
    getStatusFromCode(code) {
        const statusMap = {
            [http_status_codes_1.StatusCodes.BAD_REQUEST]: http_status_codes_1.ReasonPhrases.BAD_REQUEST.toUpperCase(),
            [http_status_codes_1.StatusCodes.CONFLICT]: http_status_codes_1.ReasonPhrases.CONFLICT.toUpperCase(),
            [http_status_codes_1.StatusCodes.NOT_FOUND]: http_status_codes_1.ReasonPhrases.NOT_FOUND.toUpperCase(),
            [http_status_codes_1.StatusCodes.UNPROCESSABLE_ENTITY]: http_status_codes_1.ReasonPhrases.UNPROCESSABLE_ENTITY.toUpperCase(),
            [http_status_codes_1.StatusCodes.NOT_ACCEPTABLE]: http_status_codes_1.ReasonPhrases.NOT_ACCEPTABLE.toUpperCase(),
            [http_status_codes_1.StatusCodes.UNAUTHORIZED]: http_status_codes_1.ReasonPhrases.UNAUTHORIZED.toUpperCase(),
            [http_status_codes_1.StatusCodes.FORBIDDEN]: http_status_codes_1.ReasonPhrases.FORBIDDEN.toUpperCase(),
            [http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR]: http_status_codes_1.ReasonPhrases.INTERNAL_SERVER_ERROR.toUpperCase(),
        };
        const reasonPhrase = statusMap[code] || 'Error';
        return reasonPhrase.replace(/\s+/g, '_');
    }
}
exports.ApplicationException = ApplicationException;
//# sourceMappingURL=data:application/json;base64,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