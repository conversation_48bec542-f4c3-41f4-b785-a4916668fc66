export declare class ApplicationException extends Error {
    message: string;
    statusCode: number;
    details?: Array<{
        field: string;
        message: string;
    }>;
    readonly exceptionName: string;
    constructor(message: string, details?: Array<{
        field: string;
        message: string;
    }> | null, statusCode?: number, exceptionName?: string);
    get status(): string;
    private getStatusFromCode;
}
