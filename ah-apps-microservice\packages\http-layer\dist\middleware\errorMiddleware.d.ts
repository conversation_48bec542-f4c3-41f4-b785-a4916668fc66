import { APIGatewayProxyEventV2, APIGatewayProxyResultV2, Context } from 'aws-lambda';
import { ValidationError } from 'class-validator';
export interface ErrorMiddleware {
    handle(error: Error | ValidationError[], event: APIGatewayProxyEventV2, context: Context): APIGatewayProxyResultV2;
}
export declare class ValidationErrorMiddleware implements ErrorMiddleware {
    handle(error: Error | ValidationError[], event: APIGatewayProxyEventV2, context: Context): APIGatewayProxyResultV2;
    private mapValidationErrors;
}
export declare class DatabaseErrorMiddleware implements ErrorMiddleware {
    handle(error: Error | ValidationError[], event: APIGatewayProxyEventV2, context: Context): APIGatewayProxyResultV2;
}
export declare class AuthErrorMiddleware implements ErrorMiddleware {
    handle(error: Error | ValidationError[], event: APIGatewayProxyEventV2, context: Context): APIGatewayProxyResultV2;
}
export declare class ErrorMiddlewareFactory {
    private static middlewares;
    static register(type: string, middleware: ErrorMiddleware): void;
    static get(type: string): ErrorMiddleware | undefined;
    static getAll(): Map<string, ErrorMiddleware>;
}
