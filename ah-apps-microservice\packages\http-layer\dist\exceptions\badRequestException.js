"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BadRequestException = void 0;
const http_status_codes_1 = require("http-status-codes");
const applicationException_1 = require("./applicationException");
class BadRequestException extends applicationException_1.ApplicationException {
    constructor(message = 'Solicitud incorrecta', details) {
        super(message, details, http_status_codes_1.StatusCodes.BAD_REQUEST, 'BadRequestException');
    }
}
exports.BadRequestException = BadRequestException;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmFkUmVxdWVzdEV4Y2VwdGlvbi5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9leGNlcHRpb25zL2JhZFJlcXVlc3RFeGNlcHRpb24udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEseURBQStDO0FBQy9DLGlFQUE2RDtBQUU3RCxNQUFhLG1CQUFvQixTQUFRLDJDQUFvQjtJQUMzRCxZQUNFLFVBQWtCLHNCQUFzQixFQUN4QyxPQUFtRDtRQUVuRCxLQUFLLENBQUMsT0FBTyxFQUFFLE9BQU8sRUFBRSwrQkFBVyxDQUFDLFdBQVcsRUFBRSxxQkFBcUIsQ0FBQyxDQUFBO0lBQ3pFLENBQUM7Q0FDRjtBQVBELGtEQU9DIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU3RhdHVzQ29kZXMgfSBmcm9tICdodHRwLXN0YXR1cy1jb2RlcydcclxuaW1wb3J0IHsgQXBwbGljYXRpb25FeGNlcHRpb24gfSBmcm9tICcuL2FwcGxpY2F0aW9uRXhjZXB0aW9uJ1xyXG5cclxuZXhwb3J0IGNsYXNzIEJhZFJlcXVlc3RFeGNlcHRpb24gZXh0ZW5kcyBBcHBsaWNhdGlvbkV4Y2VwdGlvbiB7XHJcbiAgY29uc3RydWN0b3IoXHJcbiAgICBtZXNzYWdlOiBzdHJpbmcgPSAnU29saWNpdHVkIGluY29ycmVjdGEnLFxyXG4gICAgZGV0YWlscz86IEFycmF5PHsgZmllbGQ6IHN0cmluZywgbWVzc2FnZTogc3RyaW5nIH0+XHJcbiAgKSB7XHJcbiAgICBzdXBlcihtZXNzYWdlLCBkZXRhaWxzLCBTdGF0dXNDb2Rlcy5CQURfUkVRVUVTVCwgJ0JhZFJlcXVlc3RFeGNlcHRpb24nKVxyXG4gIH1cclxufVxyXG4iXX0=