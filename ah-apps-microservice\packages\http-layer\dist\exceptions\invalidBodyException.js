"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvalidBodyException = void 0;
const http_status_codes_1 = require("http-status-codes");
const applicationException_1 = require("./applicationException");
class InvalidBodyException extends applicationException_1.ApplicationException {
    constructor(message = 'Cuerpo de la solicitud inválido', details) {
        super(message, details, http_status_codes_1.StatusCodes.UNPROCESSABLE_ENTITY, 'InvalidBodyException');
    }
}
exports.InvalidBodyException = InvalidBodyException;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW52YWxpZEJvZHlFeGNlcHRpb24uanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvZXhjZXB0aW9ucy9pbnZhbGlkQm9keUV4Y2VwdGlvbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSx5REFBK0M7QUFDL0MsaUVBQTZEO0FBRTdELE1BQWEsb0JBQXFCLFNBQVEsMkNBQW9CO0lBQzVELFlBQ0UsVUFBa0IsaUNBQWlDLEVBQ25ELE9BQW1EO1FBRW5ELEtBQUssQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLCtCQUFXLENBQUMsb0JBQW9CLEVBQUUsc0JBQXNCLENBQUMsQ0FBQTtJQUNuRixDQUFDO0NBQ0Y7QUFQRCxvREFPQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFN0YXR1c0NvZGVzIH0gZnJvbSAnaHR0cC1zdGF0dXMtY29kZXMnXHJcbmltcG9ydCB7IEFwcGxpY2F0aW9uRXhjZXB0aW9uIH0gZnJvbSAnLi9hcHBsaWNhdGlvbkV4Y2VwdGlvbidcclxuXHJcbmV4cG9ydCBjbGFzcyBJbnZhbGlkQm9keUV4Y2VwdGlvbiBleHRlbmRzIEFwcGxpY2F0aW9uRXhjZXB0aW9uIHtcclxuICBjb25zdHJ1Y3RvcihcclxuICAgIG1lc3NhZ2U6IHN0cmluZyA9ICdDdWVycG8gZGUgbGEgc29saWNpdHVkIGludsOhbGlkbycsXHJcbiAgICBkZXRhaWxzPzogQXJyYXk8eyBmaWVsZDogc3RyaW5nLCBtZXNzYWdlOiBzdHJpbmcgfT5cclxuICApIHtcclxuICAgIHN1cGVyKG1lc3NhZ2UsIGRldGFpbHMsIFN0YXR1c0NvZGVzLlVOUFJPQ0VTU0FCTEVfRU5USVRZLCAnSW52YWxpZEJvZHlFeGNlcHRpb24nKVxyXG4gIH1cclxufVxyXG4iXX0=