import { APIGatewayProxyResultV2 } from 'aws-lambda';
export interface SuccessResponseBuilder<T = any> {
    setData(data: T): SuccessResponseBuilder<T>;
    setMessage(message: string): SuccessResponseBuilder<T>;
    build(): APIGatewayProxyResultV2;
}
export declare abstract class BaseSuccessResponse<T = any> implements SuccessResponseBuilder<T> {
    protected _data?: T;
    protected _message: string;
    protected _statusCode: number;
    constructor(statusCode: number, defaultMessage: string);
    setData(data: T): SuccessResponseBuilder<T>;
    setMessage(message: string): SuccessResponseBuilder<T>;
    build(): APIGatewayProxyResultV2;
}
export declare class OkResponse<T = any> extends BaseSuccessResponse<T> {
    constructor();
}
export declare class CreatedResponse<T = any> extends BaseSuccessResponse<T> {
    constructor();
}
export declare class UpdatedResponse<T = any> extends BaseSuccessResponse<T> {
    constructor();
}
export declare class DeletedResponse extends BaseSuccessResponse {
    constructor();
    build(): APIGatewayProxyResultV2;
}
export declare class ListResponse<T = any> extends BaseSuccessResponse<T[]> {
    constructor();
}
export declare class MovedPermanentlyResponse extends BaseSuccessResponse {
    constructor();
    build(): APIGatewayProxyResultV2;
}
export declare class PermanentRedirectResponse extends BaseSuccessResponse {
    constructor();
    build(): APIGatewayProxyResultV2;
}
export declare class SuccessResponseFactory {
    static ok<T>(data?: T, message?: string): APIGatewayProxyResultV2;
    static created<T>(data?: T, message?: string): APIGatewayProxyResultV2;
    static updated<T>(data?: T, message?: string): APIGatewayProxyResultV2;
    static deleted(message?: string): APIGatewayProxyResultV2;
    static list<T>(data: T[], message?: string): APIGatewayProxyResultV2;
    static movedPermanently(location: string): APIGatewayProxyResultV2;
    static permanentRedirect(location: string): APIGatewayProxyResultV2;
    static custom<T>(statusCode: number, data?: T, message?: string): APIGatewayProxyResultV2;
}
