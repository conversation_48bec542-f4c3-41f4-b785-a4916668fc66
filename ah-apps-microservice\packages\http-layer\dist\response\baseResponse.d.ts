export interface BaseApiResponse {
    success: boolean;
}
export interface SuccessApiResponse<T = any> extends BaseApiResponse {
    success: true;
    data?: T;
    message: string;
}
export interface ErrorApiResponse extends BaseApiResponse {
    success: false;
    error: {
        code: number;
        message: string;
        status: string;
        details: Array<{
            field: string;
            message: string;
        }>;
    };
}
export type ApiResponse<T = any> = SuccessApiResponse<T> | ErrorApiResponse;
