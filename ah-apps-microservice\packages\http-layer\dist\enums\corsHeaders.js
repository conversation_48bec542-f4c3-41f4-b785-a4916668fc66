"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CORSHeaders = void 0;
var CORSHeaders;
(function (CORSHeaders) {
    CORSHeaders["ACCESS_CONTROL_ALLOW_HEADERS"] = "Access-Control-Allow-Headers";
    CORSHeaders["ORIGIN"] = "Origin";
    CORSHeaders["ACCEPT"] = "Accept";
    CORSHeaders["X_REQUESTED_WITH"] = "X-Requested-With";
    CORSHeaders["CONTENT_TYPE"] = "Content-Type";
    CORSHeaders["ACCESS_CONTROL_REQUEST_METHOD"] = "Access-Control-Request-Method";
    CORSHeaders["ACCESS_CONTROL_REQUEST_HEADERS"] = "Access-Control-Request-Headers";
    CORSHeaders["AUTHORIZATION"] = "Authorization";
    CORSHeaders["X_USERNAME"] = "x-username";
})(CORSHeaders || (exports.CORSHeaders = CORSHeaders = {}));
exports.default = CORSHeaders;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY29yc0hlYWRlcnMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvZW51bXMvY29yc0hlYWRlcnMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBUUEsSUFBWSxXQTZDWDtBQTdDRCxXQUFZLFdBQVc7SUFJckIsNEVBQTZELENBQUE7SUFLN0QsZ0NBQWlCLENBQUE7SUFLakIsZ0NBQWlCLENBQUE7SUFLakIsb0RBQXFDLENBQUE7SUFLckMsNENBQTZCLENBQUE7SUFLN0IsOEVBQStELENBQUE7SUFLL0QsZ0ZBQWlFLENBQUE7SUFLakUsOENBQStCLENBQUE7SUFLL0Isd0NBQXlCLENBQUE7QUFDM0IsQ0FBQyxFQTdDVyxXQUFXLDJCQUFYLFdBQVcsUUE2Q3RCO0FBS0Qsa0JBQWUsV0FBVyxDQUFBIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIENPUlMgSGVhZGVycyBmb3IgQWNjZXNzLUNvbnRyb2wtQWxsb3ctSGVhZGVycy5cclxuICpcclxuICogVGhlc2UgaGVhZGVycyBhcmUgY29tbW9ubHkgdXNlZCBpbiBDT1JTIHJlcXVlc3RzIGFuZCBkZWZpbmUgd2hpY2ggaGVhZGVyc1xyXG4gKiBjYW4gYmUgdXNlZCB3aGVuIG1ha2luZyB0aGUgYWN0dWFsIHJlcXVlc3QuXHJcbiAqXHJcbiAqIEBwdWJsaWNcclxuICovXHJcbmV4cG9ydCBlbnVtIENPUlNIZWFkZXJzIHtcclxuICAvKipcclxuICAgKiBTdGFuZGFyZCBDT1JTIGhlYWRlciBmb3IgYWxsb3dpbmcgaGVhZGVyc1xyXG4gICAqL1xyXG4gIEFDQ0VTU19DT05UUk9MX0FMTE9XX0hFQURFUlMgPSAnQWNjZXNzLUNvbnRyb2wtQWxsb3ctSGVhZGVycycsXHJcblxyXG4gIC8qKlxyXG4gICAqIE9yaWdpbiBoZWFkZXIgZm9yIENPUlMgcmVxdWVzdHNcclxuICAgKi9cclxuICBPUklHSU4gPSAnT3JpZ2luJyxcclxuXHJcbiAgLyoqXHJcbiAgICogQWNjZXB0IGhlYWRlciBmb3IgY29udGVudCBuZWdvdGlhdGlvblxyXG4gICAqL1xyXG4gIEFDQ0VQVCA9ICdBY2NlcHQnLFxyXG5cclxuICAvKipcclxuICAgKiBYLVJlcXVlc3RlZC1XaXRoIGhlYWRlciBmb3IgQUpBWCByZXF1ZXN0c1xyXG4gICAqL1xyXG4gIFhfUkVRVUVTVEVEX1dJVEggPSAnWC1SZXF1ZXN0ZWQtV2l0aCcsXHJcblxyXG4gIC8qKlxyXG4gICAqIENvbnRlbnQtVHlwZSBoZWFkZXIgZm9yIHJlcXVlc3QgYm9keSB0eXBlXHJcbiAgICovXHJcbiAgQ09OVEVOVF9UWVBFID0gJ0NvbnRlbnQtVHlwZScsXHJcblxyXG4gIC8qKlxyXG4gICAqIEFjY2Vzcy1Db250cm9sLVJlcXVlc3QtTWV0aG9kIGhlYWRlciBmb3IgcHJlZmxpZ2h0IHJlcXVlc3RzXHJcbiAgICovXHJcbiAgQUNDRVNTX0NPTlRST0xfUkVRVUVTVF9NRVRIT0QgPSAnQWNjZXNzLUNvbnRyb2wtUmVxdWVzdC1NZXRob2QnLFxyXG5cclxuICAvKipcclxuICAgKiBBY2Nlc3MtQ29udHJvbC1SZXF1ZXN0LUhlYWRlcnMgaGVhZGVyIGZvciBwcmVmbGlnaHQgcmVxdWVzdHNcclxuICAgKi9cclxuICBBQ0NFU1NfQ09OVFJPTF9SRVFVRVNUX0hFQURFUlMgPSAnQWNjZXNzLUNvbnRyb2wtUmVxdWVzdC1IZWFkZXJzJyxcclxuXHJcbiAgLyoqXHJcbiAgICogQXV0aG9yaXphdGlvbiBoZWFkZXIgZm9yIGF1dGhlbnRpY2F0aW9uXHJcbiAgICovXHJcbiAgQVVUSE9SSVpBVElPTiA9ICdBdXRob3JpemF0aW9uJyxcclxuXHJcbiAgLyoqXHJcbiAgICogQ3VzdG9tIHVzZXJuYW1lIGhlYWRlclxyXG4gICAqL1xyXG4gIFhfVVNFUk5BTUUgPSAneC11c2VybmFtZScsXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBAcHVibGljXHJcbiAqL1xyXG5leHBvcnQgZGVmYXVsdCBDT1JTSGVhZGVycyAiXX0=