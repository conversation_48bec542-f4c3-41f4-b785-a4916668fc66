"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorMiddlewareFactory = exports.AuthErrorMiddleware = exports.DatabaseErrorMiddleware = exports.ValidationErrorMiddleware = void 0;
const exceptionHandler_1 = require("../exceptions/exceptionHandler");
const exceptions_1 = require("../exceptions");
class ValidationErrorMiddleware {
    handle(error, event, context) {
        if (Array.isArray(error) && error.length > 0 && 'constraints' in error[0]) {
            const validationErrors = error;
            const details = this.mapValidationErrors(validationErrors);
            const validationException = new exceptions_1.BadRequestException("Errores de validación", details);
            console.warn('Validation Error:', {
                details,
                event: event.rawPath,
                context: context.awsRequestId,
                headers: event.headers
            });
            return exceptionHandler_1.exceptionHandler.handle(validationException);
        }
        if (error instanceof exceptions_1.BadRequestException) {
            console.warn('Validation Exception:', {
                message: error.message,
                details: error.details,
                event: event.rawPath,
                context: context.awsRequestId
            });
            return exceptionHandler_1.exceptionHandler.handle(error);
        }
        return exceptionHandler_1.exceptionHandler.handle(error);
    }
    mapValidationErrors(errors) {
        const details = [];
        errors.forEach(err => {
            if (err.constraints) {
                Object.values(err.constraints).forEach(msg => {
                    details.push({
                        field: err.property,
                        message: msg
                    });
                });
            }
            if (err.children && err.children.length > 0) {
                details.push(...this.mapValidationErrors(err.children));
            }
        });
        return details;
    }
}
exports.ValidationErrorMiddleware = ValidationErrorMiddleware;
class DatabaseErrorMiddleware {
    handle(error, event, context) {
        console.error('Database Error:', {
            error: error.message,
            stack: error.stack,
            event: event.rawPath,
            context: context.awsRequestId
        });
        return exceptionHandler_1.exceptionHandler.handle(error);
    }
}
exports.DatabaseErrorMiddleware = DatabaseErrorMiddleware;
class AuthErrorMiddleware {
    handle(error, event, context) {
        console.warn('Authentication Error:', {
            error: error.message,
            event: event.rawPath,
            headers: event.headers,
            context: context.awsRequestId
        });
        return exceptionHandler_1.exceptionHandler.handle(error);
    }
}
exports.AuthErrorMiddleware = AuthErrorMiddleware;
class ErrorMiddlewareFactory {
    static register(type, middleware) {
        this.middlewares.set(type, middleware);
    }
    static get(type) {
        return this.middlewares.get(type);
    }
    static getAll() {
        return new Map(this.middlewares);
    }
}
exports.ErrorMiddlewareFactory = ErrorMiddlewareFactory;
_a = ErrorMiddlewareFactory;
ErrorMiddlewareFactory.middlewares = new Map();
(() => {
    _a.middlewares.set('ValidationError', new ValidationErrorMiddleware());
    _a.middlewares.set('DatabaseError', new DatabaseErrorMiddleware());
    _a.middlewares.set('AuthError', new AuthErrorMiddleware());
})();
//# sourceMappingURL=data:application/json;base64,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