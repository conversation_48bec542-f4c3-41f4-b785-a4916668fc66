"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConflictException = void 0;
const http_status_codes_1 = require("http-status-codes");
const applicationException_1 = require("./applicationException");
class ConflictException extends applicationException_1.ApplicationException {
    constructor(message = 'Conflicto en los datos', details) {
        super(message, details, http_status_codes_1.StatusCodes.CONFLICT, 'ConflictException');
    }
}
exports.ConflictException = ConflictException;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY29uZmxpY3RFeGNlcHRpb24uanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvZXhjZXB0aW9ucy9jb25mbGljdEV4Y2VwdGlvbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSx5REFBK0M7QUFDL0MsaUVBQTZEO0FBRTdELE1BQWEsaUJBQWtCLFNBQVEsMkNBQW9CO0lBQ3pELFlBQ0UsVUFBa0Isd0JBQXdCLEVBQzFDLE9BQW1EO1FBRW5ELEtBQUssQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLCtCQUFXLENBQUMsUUFBUSxFQUFFLG1CQUFtQixDQUFDLENBQUE7SUFDcEUsQ0FBQztDQUNGO0FBUEQsOENBT0MiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdGF0dXNDb2RlcyB9IGZyb20gJ2h0dHAtc3RhdHVzLWNvZGVzJ1xyXG5pbXBvcnQgeyBBcHBsaWNhdGlvbkV4Y2VwdGlvbiB9IGZyb20gJy4vYXBwbGljYXRpb25FeGNlcHRpb24nXHJcblxyXG5leHBvcnQgY2xhc3MgQ29uZmxpY3RFeGNlcHRpb24gZXh0ZW5kcyBBcHBsaWNhdGlvbkV4Y2VwdGlvbiB7XHJcbiAgY29uc3RydWN0b3IoXHJcbiAgICBtZXNzYWdlOiBzdHJpbmcgPSAnQ29uZmxpY3RvIGVuIGxvcyBkYXRvcycsXHJcbiAgICBkZXRhaWxzPzogQXJyYXk8eyBmaWVsZDogc3RyaW5nLCBtZXNzYWdlOiBzdHJpbmcgfT5cclxuICApIHtcclxuICAgIHN1cGVyKG1lc3NhZ2UsIGRldGFpbHMsIFN0YXR1c0NvZGVzLkNPTkZMSUNULCAnQ29uZmxpY3RFeGNlcHRpb24nKVxyXG4gIH1cclxufVxyXG4iXX0=