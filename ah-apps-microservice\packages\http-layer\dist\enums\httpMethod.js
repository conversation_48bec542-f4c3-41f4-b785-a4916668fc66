"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HTTPMethod = void 0;
var HTTPMethod;
(function (HTTPMethod) {
    HTTPMethod["GET"] = "GET";
    HTTPMethod["POST"] = "POST";
    HTTPMethod["PUT"] = "PUT";
    HTTPMethod["PATCH"] = "PATCH";
    HTTPMethod["DELETE"] = "DELETE";
    HTTPMethod["HEAD"] = "HEAD";
    HTTPMethod["OPTIONS"] = "OPTIONS";
    HTTPMethod["CONNECT"] = "CONNECT";
    HTTPMethod["TRACE"] = "TRACE";
})(HTTPMethod || (exports.HTTPMethod = HTTPMethod = {}));
exports.default = HTTPMethod;
//# sourceMappingURL=data:application/json;base64,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