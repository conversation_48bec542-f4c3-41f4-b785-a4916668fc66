{"packages/ah-create-apps-lambda": {"root": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "name": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "tags": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Aplicativos": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.description": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.js": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.build": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.build.options": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test.options": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.executor": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options.script": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.scriptContent": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.runCommand": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.format": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.format.options": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/ah-create-apps-lambda/package.json", "nx/core/package-json"]}, "packages/ah-get-app-by-id-lambda": {"root": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "name": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "tags": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "tags.npm:Aplicativos": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.description": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.js": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.build": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.build.options": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test.options": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:watch": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:watch.executor": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options.script": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.scriptContent": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.runCommand": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.type-check": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.format": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.format.options": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/ah-get-app-by-id-lambda/package.json", "nx/core/package-json"]}, "packages/ah-get-app-by-key-lambda": {"root": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "name": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "tags": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "tags.npm:Aplicativos": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.description": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.js": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.build": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.build.options": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test.options": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:watch": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:watch.executor": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options.script": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.scriptContent": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.runCommand": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.type-check": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.format": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.format.options": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/ah-get-app-by-key-lambda/package.json", "nx/core/package-json"]}, "packages/ah-get-apps-lambda": {"root": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "name": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "tags": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Aplicativos": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.description": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.js": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.build": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.build.options": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test.options": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.executor": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options.script": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.scriptContent": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.runCommand": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.format": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.format.options": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/ah-get-apps-lambda/package.json", "nx/core/package-json"]}, "packages/ah-soft-delete-app-lambda": {"root": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "name": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "tags": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "tags.npm:Aplicativos": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.description": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.js": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.build": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.build.options": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test.options": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:watch": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:watch.executor": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options.script": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.scriptContent": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.runCommand": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.type-check": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.format": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.format.options": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/ah-soft-delete-app-lambda/package.json", "nx/core/package-json"]}, "packages/ah-update-apps-lambda": {"root": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "name": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "tags": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "tags.npm:Aplicativos": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.description": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.js": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.build": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.build.options": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test.options": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.executor": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.options.script": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.scriptContent": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:watch.metadata.runCommand": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.format": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.format.options": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/ah-update-apps-lambda/package.json", "nx/core/package-json"]}, "packages/common-layer": {"root": ["packages/common-layer/package.json", "nx/core/package-json"], "name": ["packages/common-layer/package.json", "nx/core/package-json"], "tags": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:IAC": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:Microservices": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:Layer": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/common-layer/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.9": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.description": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.js": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/common-layer/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/common-layer/package.json", "nx/core/package-json"], "targets": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build:nodejs": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build:nodejs.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build:nodejs.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build:nodejs.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build:nodejs.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build:nodejs.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.build:nodejs.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.postbuild:nodejs": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.postbuild:nodejs.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.postbuild:nodejs.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.postbuild:nodejs.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.postbuild:nodejs.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.postbuild:nodejs.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.postbuild:nodejs.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.type-check": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.format": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.format.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/common-layer/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/common-layer/package.json", "nx/core/package-json"]}, "packages/constructs": {"root": ["packages/constructs/package.json", "nx/core/package-json"], "name": ["packages/constructs/package.json", "nx/core/package-json"], "tags": ["packages/constructs/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/constructs/package.json", "nx/core/package-json"], "tags.npm:IAC": ["packages/constructs/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/constructs/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/constructs/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/constructs/package.json", "nx/core/package-json"], "tags.npm:Microservices": ["packages/constructs/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/constructs/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.description": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.js": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/constructs/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/constructs/package.json", "nx/core/package-json"], "targets": ["packages/constructs/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/constructs/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/constructs/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/constructs/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/constructs/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/constructs/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/constructs/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/constructs/package.json", "nx/core/package-json"], "targets.build": ["packages/constructs/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/constructs/package.json", "nx/core/package-json"], "targets.build.options": ["packages/constructs/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/constructs/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/constructs/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/constructs/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/constructs/package.json", "nx/core/package-json"], "targets.watch": ["packages/constructs/package.json", "nx/core/package-json"], "targets.watch.executor": ["packages/constructs/package.json", "nx/core/package-json"], "targets.watch.options": ["packages/constructs/package.json", "nx/core/package-json"], "targets.watch.metadata": ["packages/constructs/package.json", "nx/core/package-json"], "targets.watch.options.script": ["packages/constructs/package.json", "nx/core/package-json"], "targets.watch.metadata.scriptContent": ["packages/constructs/package.json", "nx/core/package-json"], "targets.watch.metadata.runCommand": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test.options": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/constructs/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/constructs/package.json", "nx/core/package-json"], "targets.type-check": ["packages/constructs/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/constructs/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/constructs/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/constructs/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/constructs/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/constructs/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/constructs/package.json", "nx/core/package-json"], "targets.format": ["packages/constructs/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/constructs/package.json", "nx/core/package-json"], "targets.format.options": ["packages/constructs/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/constructs/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/constructs/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/constructs/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/constructs/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/constructs/package.json", "nx/core/package-json"]}, "packages/domain": {"root": ["packages/domain/package.json", "nx/core/package-json"], "name": ["packages/domain/package.json", "nx/core/package-json"], "tags": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:IAC": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:Microservices": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:Layer": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/domain/package.json", "nx/core/package-json"], "tags.npm:Arquitectura hibrida": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/domain/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/domain/package.json", "nx/core/package-json"], "metadata.description": ["packages/domain/package.json", "nx/core/package-json"], "metadata.js": ["packages/domain/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/domain/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/domain/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/domain/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/domain/package.json", "nx/core/package-json"], "targets": ["packages/domain/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/domain/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/domain/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/domain/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/domain/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/domain/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/domain/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/domain/package.json", "nx/core/package-json"], "targets.build": ["packages/domain/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/domain/package.json", "nx/core/package-json"], "targets.build.options": ["packages/domain/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/domain/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/domain/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/domain/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/domain/package.json", "nx/core/package-json"], "targets.test": ["packages/domain/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/domain/package.json", "nx/core/package-json"], "targets.test.options": ["packages/domain/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/domain/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/domain/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/domain/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/domain/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/domain/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/domain/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/domain/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/domain/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/domain/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/domain/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/domain/package.json", "nx/core/package-json"], "targets.type-check": ["packages/domain/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/domain/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/domain/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/domain/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/domain/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/domain/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/domain/package.json", "nx/core/package-json"], "targets.format": ["packages/domain/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/domain/package.json", "nx/core/package-json"], "targets.format.options": ["packages/domain/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/domain/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/domain/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/domain/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/domain/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/domain/package.json", "nx/core/package-json"]}, "packages/http-layer": {"root": ["packages/http-layer/package.json", "nx/core/package-json"], "name": ["packages/http-layer/package.json", "nx/core/package-json"], "tags": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:IAC": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:Microservices": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:Layer": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/http-layer/package.json", "nx/core/package-json"], "tags.npm:Arquitectura hibrida": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.description": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.js": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/http-layer/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/http-layer/package.json", "nx/core/package-json"], "targets": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.build": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.build.options": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test.options": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.type-check": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.format": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.format.options": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/http-layer/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/http-layer/package.json", "nx/core/package-json"]}, "packages/lambda-package-utils": {"root": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "name": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:IAC": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:Microservices": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:Layer": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "tags.npm:Arquitectura hibrida": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.description": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.js": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.build": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.build.options": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test.options": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.type-check": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.format": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.format.options": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/lambda-package-utils/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/lambda-package-utils/package.json", "nx/core/package-json"]}, "packages/models": {"root": ["packages/models/package.json", "nx/core/package-json"], "name": ["packages/models/package.json", "nx/core/package-json"], "tags": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:IAC": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:Microservices": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:Lambda": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:Layer": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/models/package.json", "nx/core/package-json"], "tags.npm:Arquitectura hibrida": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/models/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/models/package.json", "nx/core/package-json"], "metadata.description": ["packages/models/package.json", "nx/core/package-json"], "metadata.js": ["packages/models/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/models/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/models/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/models/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/models/package.json", "nx/core/package-json"], "targets": ["packages/models/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/models/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/models/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/models/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/models/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/models/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/models/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/models/package.json", "nx/core/package-json"], "targets.build": ["packages/models/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/models/package.json", "nx/core/package-json"], "targets.build.options": ["packages/models/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/models/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/models/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/models/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/models/package.json", "nx/core/package-json"], "targets.test": ["packages/models/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/models/package.json", "nx/core/package-json"], "targets.test.options": ["packages/models/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/models/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/models/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/models/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/models/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/models/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/models/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/models/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/models/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/models/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/models/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/models/package.json", "nx/core/package-json"], "targets.type-check": ["packages/models/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/models/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/models/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/models/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/models/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/models/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/models/package.json", "nx/core/package-json"], "targets.format": ["packages/models/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/models/package.json", "nx/core/package-json"], "targets.format.options": ["packages/models/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/models/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/models/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/models/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/models/package.json", "nx/core/package-json"], "targets.lint": ["packages/models/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/models/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/models/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/models/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/models/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/models/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/models/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/models/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/models/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/models/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/models/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/models/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/models/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/models/package.json", "nx/core/package-json"]}, "packages/stack": {"root": ["packages/stack/package.json", "nx/core/package-json"], "name": ["packages/stack/package.json", "nx/core/package-json"], "tags": ["packages/stack/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/stack/package.json", "nx/core/package-json"], "tags.npm:IAC": ["packages/stack/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/stack/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/stack/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/stack/package.json", "nx/core/package-json"], "tags.npm:Microservices": ["packages/stack/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/stack/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.9": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.10": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.11": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.12": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.13": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.14": ["packages/stack/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.15": ["packages/stack/package.json", "nx/core/package-json"], "metadata.description": ["packages/stack/package.json", "nx/core/package-json"], "metadata.js": ["packages/stack/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/stack/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/stack/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/stack/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/stack/package.json", "nx/core/package-json"], "targets": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish-lambdas": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish-lambdas.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish-lambdas.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish-lambdas.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish-lambdas.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish-lambdas.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish-lambdas.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.pack-and-publish.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.clean": ["packages/stack/package.json", "nx/core/package-json"], "targets.clean.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.clean.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.clean.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.clean.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.clean.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.clean.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/stack/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.build": ["packages/stack/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.build.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.watch": ["packages/stack/package.json", "nx/core/package-json"], "targets.watch.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.watch.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.watch.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.watch.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.watch.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.watch.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.test": ["packages/stack/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.test.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.cdk": ["packages/stack/package.json", "nx/core/package-json"], "targets.cdk.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.cdk.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.cdk.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.cdk.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.cdk.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.cdk.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.deploy": ["packages/stack/package.json", "nx/core/package-json"], "targets.deploy.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.deploy.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.deploy.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.deploy.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.deploy.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.deploy.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.destroy": ["packages/stack/package.json", "nx/core/package-json"], "targets.destroy.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.destroy.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.destroy.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.destroy.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.destroy.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.destroy.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.diff": ["packages/stack/package.json", "nx/core/package-json"], "targets.diff.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.diff.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.diff.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.diff.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.diff.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.diff.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.synth": ["packages/stack/package.json", "nx/core/package-json"], "targets.synth.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.synth.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.synth.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.synth.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.synth.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.synth.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.type-check": ["packages/stack/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.format": ["packages/stack/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.format.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/stack/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/stack/package.json", "nx/core/package-json"]}, "packages/util": {"root": ["packages/util/package.json", "nx/core/package-json"], "name": ["packages/util/package.json", "nx/core/package-json"], "tags": ["packages/util/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/util/package.json", "nx/core/package-json"], "tags.npm:IAC": ["packages/util/package.json", "nx/core/package-json"], "tags.npm:AWS": ["packages/util/package.json", "nx/core/package-json"], "tags.npm:CDK": ["packages/util/package.json", "nx/core/package-json"], "tags.npm:Serverless": ["packages/util/package.json", "nx/core/package-json"], "tags.npm:Microservices": ["packages/util/package.json", "nx/core/package-json"], "tags.npm:Microsip": ["packages/util/package.json", "nx/core/package-json"], "tags.npm:Arquitectura Hibrida": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/util/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/util/package.json", "nx/core/package-json"], "metadata.description": ["packages/util/package.json", "nx/core/package-json"], "metadata.js": ["packages/util/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/util/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/util/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/util/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/util/package.json", "nx/core/package-json"], "targets": ["packages/util/package.json", "nx/core/package-json"], "targets.prebuild": ["packages/util/package.json", "nx/core/package-json"], "targets.prebuild.executor": ["packages/util/package.json", "nx/core/package-json"], "targets.prebuild.options": ["packages/util/package.json", "nx/core/package-json"], "targets.prebuild.metadata": ["packages/util/package.json", "nx/core/package-json"], "targets.prebuild.options.script": ["packages/util/package.json", "nx/core/package-json"], "targets.prebuild.metadata.scriptContent": ["packages/util/package.json", "nx/core/package-json"], "targets.prebuild.metadata.runCommand": ["packages/util/package.json", "nx/core/package-json"], "targets.build": ["packages/util/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/util/package.json", "nx/core/package-json"], "targets.build.options": ["packages/util/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/util/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/util/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/util/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/util/package.json", "nx/core/package-json"], "targets.test": ["packages/util/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/util/package.json", "nx/core/package-json"], "targets.test.options": ["packages/util/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/util/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/util/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/util/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/util/package.json", "nx/core/package-json"], "targets.test:ci": ["packages/util/package.json", "nx/core/package-json"], "targets.test:ci.executor": ["packages/util/package.json", "nx/core/package-json"], "targets.test:ci.options": ["packages/util/package.json", "nx/core/package-json"], "targets.test:ci.metadata": ["packages/util/package.json", "nx/core/package-json"], "targets.test:ci.options.script": ["packages/util/package.json", "nx/core/package-json"], "targets.test:ci.metadata.scriptContent": ["packages/util/package.json", "nx/core/package-json"], "targets.test:ci.metadata.runCommand": ["packages/util/package.json", "nx/core/package-json"], "targets.type-check": ["packages/util/package.json", "nx/core/package-json"], "targets.type-check.executor": ["packages/util/package.json", "nx/core/package-json"], "targets.type-check.options": ["packages/util/package.json", "nx/core/package-json"], "targets.type-check.metadata": ["packages/util/package.json", "nx/core/package-json"], "targets.type-check.options.script": ["packages/util/package.json", "nx/core/package-json"], "targets.type-check.metadata.scriptContent": ["packages/util/package.json", "nx/core/package-json"], "targets.type-check.metadata.runCommand": ["packages/util/package.json", "nx/core/package-json"], "targets.format": ["packages/util/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/util/package.json", "nx/core/package-json"], "targets.format.options": ["packages/util/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/util/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/util/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/util/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/util/package.json", "nx/core/package-json"], "targets.lint": ["packages/util/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/util/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/util/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/util/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/util/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/util/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/util/package.json", "nx/core/package-json"], "targets.lint:staged": ["packages/util/package.json", "nx/core/package-json"], "targets.lint:staged.executor": ["packages/util/package.json", "nx/core/package-json"], "targets.lint:staged.options": ["packages/util/package.json", "nx/core/package-json"], "targets.lint:staged.metadata": ["packages/util/package.json", "nx/core/package-json"], "targets.lint:staged.options.script": ["packages/util/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.scriptContent": ["packages/util/package.json", "nx/core/package-json"], "targets.lint:staged.metadata.runCommand": ["packages/util/package.json", "nx/core/package-json"]}}