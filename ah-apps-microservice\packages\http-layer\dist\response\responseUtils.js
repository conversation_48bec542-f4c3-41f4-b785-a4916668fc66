"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseV1 = exports.response = void 0;
const enums_1 = require("../enums");
const getAllowedMethods = () => {
    return Object.values(enums_1.HTTPMethod).join(',');
};
const getAllowedHeaders = () => {
    return Object.values(enums_1.CORSHeaders).join(', ');
};
const DEFAULT_CORS_HEADERS = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Credentials': true,
    'Access-Control-Allow-Methods': getAllowedMethods(),
    'Access-Control-Allow-Headers': getAllowedHeaders(),
};
const response = ({ statusCode, body, headers, }) => {
    console.log({ body });
    const finalHeaders = Object.assign(DEFAULT_CORS_HEADERS, headers);
    console.log({ finalHeaders });
    return {
        statusCode,
        headers: finalHeaders,
        body: typeof body === 'undefined'
            ? undefined
            : typeof body === 'string'
                ? body
                : JSON.stringify(body),
    };
};
exports.response = response;
const responseV1 = ({ statusCode, body, headers, }) => {
    console.log({ body });
    return {
        statusCode,
        headers: Object.assign(DEFAULT_CORS_HEADERS, headers),
        body: typeof body === 'string' ? body : JSON.stringify(body),
    };
};
exports.responseV1 = responseV1;
//# sourceMappingURL=data:application/json;base64,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