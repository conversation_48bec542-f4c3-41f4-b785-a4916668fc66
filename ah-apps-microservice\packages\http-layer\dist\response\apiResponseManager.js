"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiResponseManager = void 0;
const successResponse_1 = require("./successResponse");
class ApiResponseManager {
    static success(data, message = 'Operación realizada exitosamente') {
        return successResponse_1.SuccessResponseFactory.ok(data, message);
    }
    static successNoData(message = 'Operación realizada exitosamente') {
        return successResponse_1.SuccessResponseFactory.ok(undefined, message);
    }
    static list(data, message = 'Lista obtenida exitosamente') {
        return successResponse_1.SuccessResponseFactory.list(data, message);
    }
    static updated(data, message = 'Recurso actualizado exitosamente') {
        return successResponse_1.SuccessResponseFactory.updated(data, message);
    }
    static deleted(message = 'Recurso eliminado exitosamente') {
        return successResponse_1.SuccessResponseFactory.deleted(message);
    }
    static created(data, message = 'Recurso creado exitosamente') {
        return successResponse_1.SuccessResponseFactory.created(data, message);
    }
    static isSuccess(apiResponse) {
        return apiResponse.success === true;
    }
    static isError(apiResponse) {
        return apiResponse.success === false;
    }
}
exports.ApiResponseManager = ApiResponseManager;
//# sourceMappingURL=data:application/json;base64,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