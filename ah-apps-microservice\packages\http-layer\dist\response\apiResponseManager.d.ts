import { APIGatewayProxyResultV2 } from 'aws-lambda';
import { SuccessApiResponse, ApiResponse } from './baseResponse';
export declare class ApiResponseManager {
    static success<T>(data: T, message?: string): APIGatewayProxyResultV2;
    static successNoData(message?: string): APIGatewayProxyResultV2;
    static list<T>(data: T[], message?: string): APIGatewayProxyResultV2;
    static updated<T>(data: T, message?: string): APIGatewayProxyResultV2;
    static deleted(message?: string): APIGatewayProxyResultV2;
    static created<T>(data: T, message?: string): APIGatewayProxyResultV2;
    static isSuccess<T>(apiResponse: ApiResponse<T>): apiResponse is SuccessApiResponse<T>;
    static isError(apiResponse: ApiResponse): apiResponse is {
        success: false;
        error: any;
    };
}
