{"nodes": {"@microsip/ah-soft-delete-app-lambda": {"name": "@microsip/ah-soft-delete-app-lambda", "type": "lib", "data": {"root": "packages/ah-soft-delete-app-lambda", "name": "@microsip/ah-soft-delete-app-lambda", "tags": ["npm:private", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Lambda", "npm:Microsip", "npm:Arquitectura Hibrida", "npm:Aplicativos"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:watch", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Lambda para eliminar (soft delete) aplicativos dentro de la Arquitectura Híbrida de Microsip", "js": {"packageName": "@microsip/ah-soft-delete-app-lambda", "packageMain": ".dist/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "jest", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:watch": {"executor": "nx:run-script", "options": {"script": "test:watch"}, "metadata": {"scriptContent": "jest --watch", "runCommand": "yarn test:watch"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "jest --ci --coverage", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/ah-get-app-by-key-lambda": {"name": "@microsip/ah-get-app-by-key-lambda", "type": "lib", "data": {"root": "packages/ah-get-app-by-key-lambda", "name": "@microsip/ah-get-app-by-key-lambda", "tags": ["npm:private", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Lambda", "npm:Microsip", "npm:Arquitectura Hibrida", "npm:Aplicativos"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:watch", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Lambda para consultar un aplicativo por clave dentro de la Arquitectura Híbrida de Microsip", "js": {"packageName": "@microsip/ah-get-app-by-key-lambda", "packageMain": ".dist/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "jest", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:watch": {"executor": "nx:run-script", "options": {"script": "test:watch"}, "metadata": {"scriptContent": "jest --watch", "runCommand": "yarn test:watch"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "jest --ci --coverage", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/ah-get-app-by-id-lambda": {"name": "@microsip/ah-get-app-by-id-lambda", "type": "lib", "data": {"root": "packages/ah-get-app-by-id-lambda", "name": "@microsip/ah-get-app-by-id-lambda", "tags": ["npm:private", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Lambda", "npm:Microsip", "npm:Arquitectura Hibrida", "npm:Aplicativos"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:watch", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Lambda para consultar un aplicativo dentro de la Arquitectura Híbrida de Microsip", "js": {"packageName": "@microsip/ah-get-app-by-id-lambda", "packageMain": ".dist/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "jest", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:watch": {"executor": "nx:run-script", "options": {"script": "test:watch"}, "metadata": {"scriptContent": "jest --watch", "runCommand": "yarn test:watch"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "jest --ci --coverage", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/ah-create-apps-lambda": {"name": "@microsip/ah-create-apps-lambda", "type": "lib", "data": {"root": "packages/ah-create-apps-lambda", "name": "@microsip/ah-create-apps-lambda", "tags": ["npm:private", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Lambda", "npm:Microsip", "npm:Arquitectura Hibrida", "npm:Aplicativos"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:watch", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Lambda para registrar aplicativos dentro de la Arquitectura Híbrida de Microsip", "js": {"packageName": "@microsip/ah-create-apps-lambda", "packageMain": ".dist/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "jest", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:watch": {"executor": "nx:run-script", "options": {"script": "test:watch"}, "metadata": {"scriptContent": "jest --watch", "runCommand": "yarn test:watch"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "jest --ci --coverage", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/ah-update-apps-lambda": {"name": "@microsip/ah-update-apps-lambda", "type": "lib", "data": {"root": "packages/ah-update-apps-lambda", "name": "@microsip/ah-update-apps-lambda", "tags": ["npm:private", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Lambda", "npm:Microsip", "npm:Arquitectura Hibrida", "npm:Aplicativos"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:watch", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Lambda para actualizar registro de aplicativos dentro de la Arquitectura Híbrida de Microsip", "js": {"packageName": "@microsip/ah-update-apps-lambda", "packageMain": ".dist/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "jest", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:watch": {"executor": "nx:run-script", "options": {"script": "test:watch"}, "metadata": {"scriptContent": "jest --watch", "runCommand": "yarn test:watch"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "jest --ci --coverage", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/lambda-package-utils": {"name": "@microsip/lambda-package-utils", "type": "lib", "data": {"root": "packages/lambda-package-utils", "name": "@microsip/lambda-package-utils", "tags": ["npm:private", "npm:IAC", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Microservices", "npm:Lambda", "npm:Layer", "npm:Microsip", "npm:Arquitectura hibrida"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Packages lambda functions and layers for AWS lambda", "js": {"packageName": "@microsip/lambda-package-utils", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/ah-get-apps-lambda": {"name": "@microsip/ah-get-apps-lambda", "type": "lib", "data": {"root": "packages/ah-get-apps-lambda", "name": "@microsip/ah-get-apps-lambda", "tags": ["npm:private", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Lambda", "npm:Microsip", "npm:Arquitectura Hibrida", "npm:Aplicativos"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:watch", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Lambda para consultar aplicativos dentro de la Arquitectura Híbrida de Microsip", "js": {"packageName": "@microsip/ah-get-apps-lambda", "packageMain": ".dist/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "jest", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:watch": {"executor": "nx:run-script", "options": {"script": "test:watch"}, "metadata": {"scriptContent": "jest --watch", "runCommand": "yarn test:watch"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "jest --ci --coverage", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/common-layer": {"name": "@microsip/common-layer", "type": "lib", "data": {"root": "packages/common-layer", "name": "@microsip/common-layer", "tags": ["npm:private", "npm:IAC", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Microservices", "npm:Lambda", "npm:Layer", "npm:Microsip", "npm:Arquitectura Hibrida"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "build:nodejs", "postbuild:nodejs", "test", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Microsip Arquitectura Hibrida Common Lambda Layer", "js": {"packageName": "@microsip/common-layer", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf nodejs/node_modules nodejs/package.json nodejs/package-lock.json", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "echo 'No build my lord'", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "build:nodejs": {"executor": "nx:run-script", "options": {"script": "build:nodejs"}, "metadata": {"scriptContent": "scripts/build.sh", "runCommand": "yarn build:nodejs"}, "configurations": {}, "parallelism": true}, "postbuild:nodejs": {"executor": "nx:run-script", "options": {"script": "postbuild:nodejs"}, "metadata": {"scriptContent": "scripts/post-build.sh", "runCommand": "yarn postbuild:nodejs"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/constructs": {"name": "@microsip/constructs", "type": "lib", "data": {"root": "packages/constructs", "name": "@microsip/constructs", "tags": ["npm:private", "npm:IAC", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Microservices", "npm:Microsip", "npm:Arquitectura Hibrida"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "watch", "test", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Microsip Arquitectura Hibrida Apps Microservice Stack Constructs", "js": {"packageName": "@microsip/constructs", "packageMain": "dist/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "<PERSON><PERSON><PERSON> dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "watch": {"executor": "nx:run-script", "options": {"script": "watch"}, "metadata": {"scriptContent": "tsc -w", "runCommand": "yarn watch"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/http-layer": {"name": "@microsip/http-layer", "type": "lib", "data": {"root": "packages/http-layer", "name": "@microsip/http-layer", "tags": ["npm:private", "npm:IAC", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Microservices", "npm:Lambda", "npm:Layer", "npm:Microsip", "npm:Arquitectura hibrida"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Microsip Online Billing Common Http Lambda Layer", "js": {"packageName": "@microsip/http-layer", "packageExports": {".": "./src/index.ts", "./response": "./src/response/index.ts", "./exceptions": "./src/exceptions/index.ts", "./middleware": "./src/middleware/index.ts", "./enums": "./src/enums/index.ts", "./models": "./src/models/index.ts"}, "packageMain": ".dist/http-layer/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/domain": {"name": "@microsip/domain", "type": "lib", "data": {"root": "packages/domain", "name": "@microsip/domain", "tags": ["npm:private", "npm:IAC", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Microservices", "npm:Lambda", "npm:Layer", "npm:Microsip", "npm:Arquitectura hibrida"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Domain for the microsip project", "js": {"packageName": "@microsip/domain", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/models": {"name": "@microsip/models", "type": "lib", "data": {"root": "packages/models", "name": "@microsip/models", "tags": ["npm:private", "npm:IAC", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Microservices", "npm:Lambda", "npm:Layer", "npm:Microsip", "npm:Arquitectura hibrida"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Microsip Arquitectura Hibrida Common Models", "js": {"packageName": "@microsip/models", "packageMain": ".dist/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "echo 'No tests my lord'", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/stack": {"name": "@microsip/stack", "type": "lib", "data": {"root": "packages/stack", "name": "@microsip/stack", "tags": ["npm:private", "npm:IAC", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Microservices", "npm:Microsip", "npm:Arquitectura Hibrida"], "metadata": {"targetGroups": {"NPM Scripts": ["pack-and-publish-lambdas", "pack-and-publish", "clean", "prebuild", "build", "watch", "test", "cdk", "deploy", "destroy", "diff", "synth", "type-check", "format", "lint", "lint:staged"]}, "description": "Microsip Arquitectura Hibrida Apps Microservice", "js": {"packageName": "@microsip/stack", "isInPackageManagerWorkspaces": true}}, "targets": {"pack-and-publish-lambdas": {"executor": "nx:run-script", "options": {"script": "pack-and-publish-lambdas"}, "metadata": {"scriptContent": "run-s clean build pack-and-publish", "runCommand": "yarn pack-and-publish-lambdas"}, "configurations": {}, "parallelism": true}, "pack-and-publish": {"executor": "nx:run-script", "options": {"script": "pack-and-publish"}, "metadata": {"scriptContent": "ts-node -r tsconfig-paths/register scripts/run.ts", "runCommand": "yarn pack-and-publish"}, "configurations": {}, "parallelism": true}, "clean": {"executor": "nx:run-script", "options": {"script": "clean"}, "metadata": {"scriptContent": "tsc --build --clean", "runCommand": "yarn clean"}, "configurations": {}, "parallelism": true}, "prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "watch": {"executor": "nx:run-script", "options": {"script": "watch"}, "metadata": {"scriptContent": "tsc -w", "runCommand": "yarn watch"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "jest", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "cdk": {"executor": "nx:run-script", "options": {"script": "cdk"}, "metadata": {"scriptContent": "cdk", "runCommand": "yarn cdk"}, "configurations": {}, "parallelism": true}, "deploy": {"executor": "nx:run-script", "options": {"script": "deploy"}, "metadata": {"scriptContent": "cdk deploy", "runCommand": "yarn deploy"}, "configurations": {}, "parallelism": true}, "destroy": {"executor": "nx:run-script", "options": {"script": "destroy"}, "metadata": {"scriptContent": "cdk destroy", "runCommand": "yarn destroy"}, "configurations": {}, "parallelism": true}, "diff": {"executor": "nx:run-script", "options": {"script": "diff"}, "metadata": {"scriptContent": "cdk diff", "runCommand": "yarn diff"}, "configurations": {}, "parallelism": true}, "synth": {"executor": "nx:run-script", "options": {"script": "synth"}, "metadata": {"scriptContent": "cdk synth", "runCommand": "yarn synth"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@microsip/util": {"name": "@microsip/util", "type": "lib", "data": {"root": "packages/util", "name": "@microsip/util", "tags": ["npm:private", "npm:IAC", "npm:AWS", "npm:CDK", "npm:Serverless", "npm:Microservices", "npm:Microsip", "npm:Arquitectura Hibrida"], "metadata": {"targetGroups": {"NPM Scripts": ["prebuild", "build", "test", "test:ci", "type-check", "format", "lint", "lint:staged"]}, "description": "Microsip Arquitectura Hibrida Apps Microservice Utils", "js": {"packageName": "@microsip/util", "packageMain": ".dist/index.js", "isInPackageManagerWorkspaces": true}}, "targets": {"prebuild": {"executor": "nx:run-script", "options": {"script": "prebuild"}, "metadata": {"scriptContent": "rimraf .dist", "runCommand": "yarn prebuild"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "tsc", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "jest", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "test:ci": {"executor": "nx:run-script", "options": {"script": "test:ci"}, "metadata": {"scriptContent": "jest -u --detect<PERSON><PERSON><PERSON><PERSON>les --forceExit", "runCommand": "yarn test:ci"}, "configurations": {}, "parallelism": true}, "type-check": {"executor": "nx:run-script", "options": {"script": "type-check"}, "metadata": {"scriptContent": "tsc --pretty --noEmit", "runCommand": "yarn type-check"}, "configurations": {}, "parallelism": true}, "format": {"executor": "nx:run-script", "options": {"script": "format"}, "metadata": {"scriptContent": "prettier --write **/*.ts", "runCommand": "yarn format"}, "configurations": {}, "parallelism": true}, "lint": {"executor": "nx:run-script", "options": {"script": "lint"}, "metadata": {"scriptContent": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "runCommand": "yarn lint"}, "configurations": {}, "parallelism": true}, "lint:staged": {"executor": "nx:run-script", "options": {"script": "lint:staged"}, "metadata": {"scriptContent": "lint-staged", "runCommand": "yarn lint:staged"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}}, "externalNodes": {}, "dependencies": {"@microsip/ah-soft-delete-app-lambda": [{"source": "@microsip/ah-soft-delete-app-lambda", "target": "@microsip/domain", "type": "static"}, {"source": "@microsip/ah-soft-delete-app-lambda", "target": "@microsip/models", "type": "static"}, {"source": "@microsip/ah-soft-delete-app-lambda", "target": "@microsip/http-layer", "type": "static"}], "@microsip/ah-get-app-by-key-lambda": [{"source": "@microsip/ah-get-app-by-key-lambda", "target": "@microsip/domain", "type": "static"}, {"source": "@microsip/ah-get-app-by-key-lambda", "target": "@microsip/models", "type": "static"}, {"source": "@microsip/ah-get-app-by-key-lambda", "target": "@microsip/http-layer", "type": "static"}], "@microsip/ah-get-app-by-id-lambda": [{"source": "@microsip/ah-get-app-by-id-lambda", "target": "@microsip/domain", "type": "static"}, {"source": "@microsip/ah-get-app-by-id-lambda", "target": "@microsip/models", "type": "static"}, {"source": "@microsip/ah-get-app-by-id-lambda", "target": "@microsip/http-layer", "type": "static"}], "@microsip/ah-create-apps-lambda": [{"source": "@microsip/ah-create-apps-lambda", "target": "@microsip/domain", "type": "static"}, {"source": "@microsip/ah-create-apps-lambda", "target": "@microsip/models", "type": "static"}, {"source": "@microsip/ah-create-apps-lambda", "target": "@microsip/http-layer", "type": "static"}], "@microsip/ah-update-apps-lambda": [{"source": "@microsip/ah-update-apps-lambda", "target": "@microsip/domain", "type": "static"}, {"source": "@microsip/ah-update-apps-lambda", "target": "@microsip/models", "type": "static"}, {"source": "@microsip/ah-update-apps-lambda", "target": "@microsip/http-layer", "type": "static"}], "@microsip/lambda-package-utils": [], "@microsip/ah-get-apps-lambda": [{"source": "@microsip/ah-get-apps-lambda", "target": "@microsip/domain", "type": "static"}, {"source": "@microsip/ah-get-apps-lambda", "target": "@microsip/http-layer", "type": "static"}, {"source": "@microsip/ah-get-apps-lambda", "target": "@microsip/models", "type": "static"}], "@microsip/common-layer": [], "@microsip/constructs": [{"source": "@microsip/constructs", "target": "@microsip/ah-create-apps-lambda", "type": "static"}, {"source": "@microsip/constructs", "target": "@microsip/ah-get-app-by-key-lambda", "type": "static"}, {"source": "@microsip/constructs", "target": "@microsip/ah-get-app-by-id-lambda", "type": "static"}, {"source": "@microsip/constructs", "target": "@microsip/ah-get-apps-lambda", "type": "static"}, {"source": "@microsip/constructs", "target": "@microsip/ah-soft-delete-app-lambda", "type": "static"}, {"source": "@microsip/constructs", "target": "@microsip/ah-update-apps-lambda", "type": "static"}, {"source": "@microsip/constructs", "target": "@microsip/util", "type": "static"}], "@microsip/http-layer": [], "@microsip/domain": [{"source": "@microsip/domain", "target": "@microsip/models", "type": "static"}], "@microsip/models": [{"source": "@microsip/models", "target": "@microsip/http-layer", "type": "static"}], "@microsip/stack": [{"source": "@microsip/stack", "target": "@microsip/lambda-package-utils", "type": "static"}, {"source": "@microsip/stack", "target": "@microsip/util", "type": "static"}], "@microsip/util": []}, "version": "6.0", "errors": [], "computedAt": 1751086538883}