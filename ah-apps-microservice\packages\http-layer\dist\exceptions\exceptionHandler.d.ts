import { APIGatewayProxyResultV2 } from 'aws-lambda';
import { ApplicationException } from './applicationException';
export interface ExceptionHandler {
    handle(exception: Error): APIGatewayProxyResultV2;
}
export declare class ApplicationExceptionHandler implements ExceptionHandler {
    handle(exception: ApplicationException): APIGatewayProxyResultV2;
}
export declare class GenericExceptionHandler implements ExceptionHandler {
    handle(exception: Error): APIGatewayProxyResultV2;
}
export declare class ExceptionHandlerManager {
    private handlers;
    constructor();
    registerHandler(exceptionType: string, handler: ExceptionHandler): void;
    handle(exception: Error): APIGatewayProxyResultV2;
}
export declare const exceptionHandler: ExceptionHandlerManager;
