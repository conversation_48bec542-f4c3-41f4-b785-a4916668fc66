"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.restPostApiInvocationWithDifferentTypes = exports.restGetItemApiInvocation = exports.restGetListApiInvocation = exports.restPutApiInvocation = exports.restPostApiInvocation = exports.updateItem = exports.createItemNoItem = exports.createItem = exports.initializeItemNoContext = exports.initializeItem = exports.handleAsyncV2 = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const exceptions_1 = require("./exceptions");
const apiResponseManager_1 = require("./response/apiResponseManager");
const middleware_1 = require("./middleware");
const handleAsyncV2 = (handler) => {
    return async (event, context) => {
        console.log('handleAsyncV2');
        console.log({ event: JSON.stringify(event, null, 2) });
        console.log({ requestContext: JSON.stringify(event?.requestContext, null, 2) });
        console.log({ context: JSON.stringify(context, null, 2) });
        try {
            return await handler(event, context);
        }
        catch (e) {
            console.error('Unhandled Error:', e);
            const errorMiddleware = middleware_1.ErrorMiddlewareFactory.get('ValidationError');
            if (errorMiddleware && Array.isArray(e) && e.length > 0 && 'constraints' in e[0]) {
                return errorMiddleware.handle(e, event, context);
            }
            const genericMiddleware = middleware_1.ErrorMiddlewareFactory.get('DatabaseError');
            if (genericMiddleware) {
                return genericMiddleware.handle(e, event, context);
            }
            const { exceptionHandler } = await Promise.resolve().then(() => __importStar(require('./exceptions')));
            return exceptionHandler.handle(e);
        }
    };
};
exports.handleAsyncV2 = handleAsyncV2;
const initializeItem = (item, ctx) => {
    const currentDate = new Date();
    currentDate.setHours(currentDate.getHours() - 6);
    item.dateHourCreate = currentDate;
    item.createdBy = ctx.identity?.cognitoIdentityId || "";
    item.dateHourUpdate = currentDate;
    item.updatedBy = ctx.identity?.cognitoIdentityId || "";
    return item;
};
exports.initializeItem = initializeItem;
const initializeItemNoContext = (item, username) => {
    const currentDate = new Date();
    currentDate.setHours(currentDate.getHours() - 6);
    item.dateHourCreate = currentDate;
    item.createdBy = item.createdBy || username;
    item.dateHourUpdate = currentDate;
    item.updatedBy = item.updatedBy || username;
    item.deleted = item.deleted === undefined ? false : item.deleted;
    return item;
};
exports.initializeItemNoContext = initializeItemNoContext;
const createItem = async (example, ctx, event) => {
    if (!event.body) {
        throw new exceptions_1.NoBodyProvidedException();
    }
    const newItem = new example();
    let itemTransformed = (0, class_transformer_1.plainToClassFromExist)(newItem, JSON.parse(event.body));
    console.log({ itemTransformed });
    const errors = await (0, class_validator_1.validate)(itemTransformed);
    console.log({ errors });
    if (errors && errors.length > 0) {
        throw errors;
    }
    itemTransformed = (0, exports.initializeItem)(itemTransformed, ctx);
    const username = event.headers['X-USERNAME'] || event.headers['x-username'];
    itemTransformed.createdBy = username || itemTransformed.createdBy;
    itemTransformed.updatedBy = username || itemTransformed.updatedBy;
    return itemTransformed;
};
exports.createItem = createItem;
const createItemNoItem = async (example, event) => {
    if (!event.body) {
        throw new exceptions_1.NoBodyProvidedException();
    }
    const newItem = new example();
    let itemTransformed = (0, class_transformer_1.plainToClassFromExist)(newItem, JSON.parse(event.body));
    console.log({ itemTransformed });
    const errors = await (0, class_validator_1.validate)(itemTransformed);
    console.log({ errors });
    if (errors && errors.length > 0) {
        throw errors;
    }
    return itemTransformed;
};
exports.createItemNoItem = createItemNoItem;
const updateItem = async (example, ctx, event) => {
    if (!event.body) {
        throw new exceptions_1.NoBodyProvidedException();
    }
    const newItem = new example();
    let itemTransformed = (0, class_transformer_1.plainToClassFromExist)(newItem, JSON.parse(event.body));
    console.log({ itemTransformed });
    const errors = await (0, class_validator_1.validate)(itemTransformed);
    console.log({ errors });
    if (errors && errors.length > 0) {
        throw errors;
    }
    itemTransformed = (0, exports.initializeItem)(itemTransformed, ctx);
    const username = event.headers['X-USERNAME'] || event.headers['x-username'];
    itemTransformed.updatedBy = username || itemTransformed.updatedBy;
    return itemTransformed;
};
exports.updateItem = updateItem;
const restPostApiInvocation = (modelExample, saveFunction, successMessage) => (0, exports.handleAsyncV2)(async (event, ctx) => {
    const item = await (0, exports.createItem)(modelExample, ctx, event);
    const savedItem = await saveFunction(item);
    return apiResponseManager_1.ApiResponseManager.created(savedItem, successMessage);
});
exports.restPostApiInvocation = restPostApiInvocation;
const restPutApiInvocation = (modelExample, updateFunction, successMessage) => (0, exports.handleAsyncV2)(async (event, ctx) => {
    if (!event?.pathParameters?.id)
        throw new exceptions_1.NotFoundException();
    const item = await (0, exports.updateItem)(modelExample, ctx, event);
    const savedItem = await updateFunction(event.pathParameters.id, item);
    return apiResponseManager_1.ApiResponseManager.updated(savedItem, successMessage);
});
exports.restPutApiInvocation = restPutApiInvocation;
const restGetListApiInvocation = (listFunction, successMessage) => (0, exports.handleAsyncV2)(async () => {
    const items = await listFunction();
    return items ? apiResponseManager_1.ApiResponseManager.list(items, successMessage) : apiResponseManager_1.ApiResponseManager.successNoData();
});
exports.restGetListApiInvocation = restGetListApiInvocation;
const restGetItemApiInvocation = (getFunction, successMessage) => (0, exports.handleAsyncV2)(async (event) => {
    if (!event?.pathParameters?.id)
        throw new exceptions_1.NotFoundException();
    const item = await getFunction(event.pathParameters.id);
    return item ? apiResponseManager_1.ApiResponseManager.list([item], successMessage) : apiResponseManager_1.ApiResponseManager.successNoData();
});
exports.restGetItemApiInvocation = restGetItemApiInvocation;
const restPostApiInvocationWithDifferentTypes = (inputModelExample, saveFunction, successMessage) => (0, exports.handleAsyncV2)(async (event) => {
    const item = await (0, exports.createItemNoItem)(inputModelExample, event);
    const savedItem = await saveFunction(item);
    return apiResponseManager_1.ApiResponseManager.created(savedItem, successMessage);
});
exports.restPostApiInvocationWithDifferentTypes = restPostApiInvocationWithDifferentTypes;
//# sourceMappingURL=data:application/json;base64,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