"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnauthorizedException = void 0;
const http_status_codes_1 = require("http-status-codes");
const applicationException_1 = require("./applicationException");
class UnauthorizedException extends applicationException_1.ApplicationException {
    constructor(message = 'No autorizado', details) {
        super(message, details, http_status_codes_1.StatusCodes.UNAUTHORIZED, 'UnauthorizedException');
    }
}
exports.UnauthorizedException = UnauthorizedException;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidW5hdXRob3JpemVkRXhjZXB0aW9uLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc3JjL2V4Y2VwdGlvbnMvdW5hdXRob3JpemVkRXhjZXB0aW9uLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7OztBQUFBLHlEQUErQztBQUMvQyxpRUFBNkQ7QUFFN0QsTUFBYSxxQkFBc0IsU0FBUSwyQ0FBb0I7SUFDN0QsWUFDRSxVQUFrQixlQUFlLEVBQ2pDLE9BQW1EO1FBRW5ELEtBQUssQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLCtCQUFXLENBQUMsWUFBWSxFQUFFLHVCQUF1QixDQUFDLENBQUE7SUFDNUUsQ0FBQztDQUNGO0FBUEQsc0RBT0MiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdGF0dXNDb2RlcyB9IGZyb20gJ2h0dHAtc3RhdHVzLWNvZGVzJ1xyXG5pbXBvcnQgeyBBcHBsaWNhdGlvbkV4Y2VwdGlvbiB9IGZyb20gJy4vYXBwbGljYXRpb25FeGNlcHRpb24nXHJcblxyXG5leHBvcnQgY2xhc3MgVW5hdXRob3JpemVkRXhjZXB0aW9uIGV4dGVuZHMgQXBwbGljYXRpb25FeGNlcHRpb24ge1xyXG4gIGNvbnN0cnVjdG9yKFxyXG4gICAgbWVzc2FnZTogc3RyaW5nID0gJ05vIGF1dG9yaXphZG8nLFxyXG4gICAgZGV0YWlscz86IEFycmF5PHsgZmllbGQ6IHN0cmluZywgbWVzc2FnZTogc3RyaW5nIH0+XHJcbiAgKSB7XHJcbiAgICBzdXBlcihtZXNzYWdlLCBkZXRhaWxzLCBTdGF0dXNDb2Rlcy5VTkFVVEhPUklaRUQsICdVbmF1dGhvcml6ZWRFeGNlcHRpb24nKVxyXG4gIH1cclxufVxyXG4iXX0=