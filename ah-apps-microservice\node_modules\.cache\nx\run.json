{"run": {"command": "lerna run --stream build", "startTime": "2025-06-28T04:55:39.075Z", "endTime": "2025-06-28T04:56:23.624Z", "inner": false}, "tasks": [{"taskId": "@microsip/common-layer:build", "target": "build", "projectName": "@microsip/common-layer", "hash": "17648420356922053568", "startTime": "2025-06-28T04:55:39.519Z", "endTime": "2025-06-28T04:55:51.220Z", "params": "", "cacheStatus": "cache-miss", "status": 0}, {"taskId": "@microsip/util:build", "target": "build", "projectName": "@microsip/util", "hash": "1779731370666450389", "startTime": "2025-06-28T04:55:39.519Z", "endTime": "2025-06-28T04:55:57.281Z", "params": "", "cacheStatus": "cache-miss", "status": 0}, {"taskId": "@microsip/http-layer:build", "target": "build", "projectName": "@microsip/http-layer", "hash": "7219638883894174789", "startTime": "2025-06-28T04:55:39.519Z", "endTime": "2025-06-28T04:56:03.686Z", "params": "", "cacheStatus": "cache-miss", "status": 0}, {"taskId": "@microsip/lambda-package-utils:build", "target": "build", "projectName": "@microsip/lambda-package-utils", "hash": "15788801519766150733", "startTime": "2025-06-28T04:55:39.519Z", "endTime": "2025-06-28T04:56:06.067Z", "params": "", "cacheStatus": "cache-miss", "status": 1}, {"taskId": "@microsip/models:build", "target": "build", "projectName": "@microsip/models", "hash": "13084709448717789053", "startTime": "2025-06-28T04:56:03.726Z", "endTime": "2025-06-28T04:56:23.619Z", "params": "", "cacheStatus": "cache-miss", "status": 0}]}